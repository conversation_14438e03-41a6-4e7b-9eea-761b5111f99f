<template>
  <div class="container mt-5">
    <div class="row justify-content-center">
      <div class="col-md-8">
        <div class="card">
          <div class="card-header">
            <h3 class="mb-0">Google Analytics 测试页面</h3>
          </div>
          <div class="card-body">
            <p class="text-muted">此页面用于测试Google Analytics集成是否正常工作</p>
            
            <div class="alert alert-info">
              <strong>当前追踪ID:</strong> G-PN1XZ4X9VG
            </div>

            <div class="row">
              <div class="col-md-6">
                <h5>测试事件</h5>
                <div class="d-grid gap-2">
                  <button 
                    class="btn btn-primary" 
                    @click="testCardGeneration"
                  >
                    测试卡片生成事件
                  </button>
                  
                  <button 
                    class="btn btn-success" 
                    @click="testCardDownload"
                  >
                    测试卡片下载事件
                  </button>
                  
                  <button 
                    class="btn btn-warning" 
                    @click="testLanguageChange"
                  >
                    测试语言切换事件
                  </button>
                  
                  <button 
                    class="btn btn-info" 
                    @click="testTemplateChange"
                  >
                    测试模板切换事件
                  </button>
                </div>
              </div>
              
              <div class="col-md-6">
                <h5>GA状态检查</h5>
                <div class="alert" :class="gaStatus.class">
                  <strong>状态:</strong> {{ gaStatus.message }}
                </div>
                
                <div class="alert alert-secondary">
                  <strong>DataLayer 事件数:</strong> {{ dataLayerCount }}
                </div>
                
                <button 
                  class="btn btn-outline-secondary btn-sm" 
                  @click="checkGAStatus"
                >
                  刷新状态
                </button>
              </div>
            </div>

            <div class="mt-4">
              <h5>事件日志</h5>
              <div class="bg-light p-3 rounded" style="max-height: 300px; overflow-y: auto;">
                <div v-if="eventLog.length === 0" class="text-muted">
                  暂无事件记录
                </div>
                <div v-for="(event, index) in eventLog" :key="index" class="mb-2">
                  <small class="text-muted">{{ event.timestamp }}</small><br>
                  <strong>{{ event.type }}</strong>: {{ event.details }}
                </div>
              </div>
              <button 
                class="btn btn-outline-danger btn-sm mt-2" 
                @click="clearLog"
              >
                清空日志
              </button>
            </div>

            <div class="mt-4">
              <nuxt-link to="/" class="btn btn-secondary">
                返回主页
              </nuxt-link>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'AnalyticsTest',
  
  head() {
    const currentLocale = this.$i18n?.locale || 'en'
    const baseUrl = this.$config?.baseURL || 'https://yugiohcardmaker.org'

    // Generate canonical URL based on current locale
    const canonicalUrl = currentLocale === 'en'
      ? `${baseUrl}/analytics-test`
      : `${baseUrl}/${currentLocale}/analytics-test`

    // Generate hreflang links for all supported languages
    const languages = ['en', 'zh', 'ja', 'de', 'fr', 'ko', 'pt', 'es', 'el', 'th', 'ru', 'vi']
    const hreflangLinks = languages.map(lang => ({
      hid: `alternate-${lang}`,
      rel: 'alternate',
      hreflang: lang,
      href: lang === 'en' ? `${baseUrl}/analytics-test` : `${baseUrl}/${lang}/analytics-test`
    }))

    // Add x-default hreflang
    hreflangLinks.push({
      hid: 'alternate-x-default',
      rel: 'alternate',
      hreflang: 'x-default',
      href: `${baseUrl}/analytics-test`
    })

    return {
      title: 'Google Analytics 测试 - Yu-Gi-Oh! Card Maker',
      htmlAttrs: {
        lang: currentLocale
      },
      meta: [
        { hid: 'description', name: 'description', content: 'Google Analytics集成测试页面' },
        { hid: 'robots', name: 'robots', content: 'noindex, nofollow' }, // 测试页面不需要被搜索引擎索引
        { hid: 'og:title', property: 'og:title', content: 'Google Analytics 测试 - Yu-Gi-Oh! Card Maker' },
        { hid: 'og:description', property: 'og:description', content: 'Google Analytics集成测试页面' },
        { hid: 'og:url', property: 'og:url', content: canonicalUrl },
        { hid: 'og:locale', property: 'og:locale', content: currentLocale }
      ],
      link: [
        // Canonical URL for current page
        { hid: 'canonical', rel: 'canonical', href: canonicalUrl },
        // Hreflang links for all language versions
        ...hreflangLinks
      ]
    }
  },

  data() {
    return {
      gaStatus: {
        class: 'alert-warning',
        message: '检查中...'
      },
      dataLayerCount: 0,
      eventLog: []
    }
  },

  mounted() {
    this.$nextTick(() => {
      this.checkGAStatus()

      // 监听 dataLayer 变化
      if (window.dataLayer) {
        this.dataLayerCount = window.dataLayer.length
      }
    })
  },

  methods: {
    checkGAStatus() {
      if (process.client) {
        // 检查 gtag 函数是否存在
        if (typeof window.gtag === 'function') {
          this.gaStatus = {
            class: 'alert-success',
            message: 'Google Analytics 已正确加载'
          }
        } else {
          this.gaStatus = {
            class: 'alert-danger',
            message: 'Google Analytics 未加载'
          }
        }

        // 更新 dataLayer 计数
        if (window.dataLayer) {
          this.dataLayerCount = window.dataLayer.length
        }
      }
    },

    testCardGeneration() {
      if (typeof window.trackCardGeneration === 'function') {
        window.trackCardGeneration('Monster', 'zh')
        this.logEvent('卡片生成', 'Monster卡片 (中文)')
      } else {
        this.logEvent('错误', '卡片生成追踪函数不存在')
      }
    },

    testCardDownload() {
      if (typeof window.trackCardDownload === 'function') {
        window.trackCardDownload('png')
        this.logEvent('卡片下载', 'PNG格式')
      } else {
        this.logEvent('错误', '卡片下载追踪函数不存在')
      }
    },

    testLanguageChange() {
      if (typeof window.trackLanguageChange === 'function') {
        window.trackLanguageChange('en', 'zh')
        this.logEvent('语言切换', '中文 → 英文')
      } else {
        this.logEvent('错误', '语言切换追踪函数不存在')
      }
    },

    testTemplateChange() {
      if (typeof window.trackTemplateChange === 'function') {
        window.trackTemplateChange('Spell')
        this.logEvent('模板切换', '魔法卡模板')
      } else {
        this.logEvent('错误', '模板切换追踪函数不存在')
      }
    },

    logEvent(type, details) {
      const timestamp = new Date().toLocaleTimeString()
      this.eventLog.unshift({
        timestamp,
        type,
        details
      })
      
      // 限制日志条数
      if (this.eventLog.length > 50) {
        this.eventLog = this.eventLog.slice(0, 50)
      }

      // 更新 dataLayer 计数
      if (process.client && window.dataLayer) {
        this.dataLayerCount = window.dataLayer.length
      }
    },

    clearLog() {
      this.eventLog = []
    }
  }
}
</script>

<style scoped>
.card {
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.btn {
  transition: all 0.2s ease;
}

.btn:hover {
  transform: translateY(-1px);
}
</style>
