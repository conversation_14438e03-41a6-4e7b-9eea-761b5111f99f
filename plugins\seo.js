// SEO plugin for multi-language support
export default function ({ app, route, $config }) {
  // Only run on client-side to avoid SSR issues
  if (process.client) {
    // Wait for i18n to be ready
    app.i18n.onLanguageSwitched = (oldLocale, newLocale) => {
      updateSEOTags(newLocale, route, $config)
    }
    
    // Update SEO tags when route changes
    app.router.afterEach((to) => {
      const locale = app.i18n.locale || 'en'
      updateSEOTags(locale, to, $config)
    })
    
    // Initial SEO update
    setTimeout(() => {
      const locale = app.i18n.locale || 'en'
      updateSEOTags(locale, route, $config)
    }, 100)
  }
}

function updateSEOTags(locale, route, $config) {
  const baseUrl = $config.baseURL || 'https://yugiohcardmaker.org'
  
  // Page titles for different languages
  const titles = {
    en: 'Best Yu-Gi-Oh! Card Maker - Free Online Card Maker',
    zh: '最佳遊戲王卡片製造機 - 免費在綫設計工具',
    ja: 'ベスト遊戯王カードメーカー - 無料オンライン作成ツール',
    de: 'Bester Yu-Gi-Oh! Kartenmacher - Kostenloser Online-Kartenmacher',
    fr: '<PERSON><PERSON>ur Créateur de Cartes Yu-Gi-Oh! - Créateur de Cartes Gratuit en Ligne',
    ko: '최고의 유희왕 카드 메이커 - 무료 온라인 카드 제작기',
    pt: 'Melhor Criador de Cartas Yu-Gi-Oh! - Criador de Cartas Gratuito Online',
    es: 'Mejor Generador de Cartas Yu-Gi-Oh! - Creador de Cartas Gratuito en Línea',
    el: 'Καλύτερος Δημιουργός Καρτών Yu-Gi-Oh! - Δωρεάν Online Δημιουργός Καρτών',
    th: 'เครื่องมือสร้างการ์ด Yu-Gi-Oh! ที่ดีที่สุด - เครื่องมือสร้างการ์ดออนไลน์ฟรี',
    ru: 'Лучший Создатель Карт Yu-Gi-Oh! - Бесплатный Онлайн Создатель Карт',
    vi: 'Trình Tạo Thẻ Yu-Gi-Oh! Tốt Nhất - Trình Tạo Thẻ Trực Tuyến Miễn Phí'
  }

  // Page descriptions for different languages
  const descriptions = {
    en: 'Create custom Yu-Gi-Oh! cards with our free online card maker. Support for all card types including monsters, spells, traps, pendulum, and link monsters.',
    zh: '使用我們的免費在線卡片製造機創建自定義遊戲王卡片。支援所有卡片類型，包括怪獸、魔法、陷阱、靈擺和連結怪獸。',
    ja: '無料のオンラインカードメーカーでカスタム遊戯王カードを作成。モンスター、魔法、罠、ペンデュラム、リンクモンスターなど全てのカードタイプに対応。',
    de: 'Erstellen Sie benutzerdefinierte Yu-Gi-Oh! Karten mit unserem kostenlosen Online-Kartenmacher. Unterstützung für alle Kartentypen einschließlich Monster, Zauber, Fallen, Pendel und Link-Monster.',
    fr: 'Créez des cartes Yu-Gi-Oh! personnalisées avec notre créateur de cartes en ligne gratuit. Support pour tous les types de cartes y compris les monstres, sorts, pièges, pendule et monstres lien.',
    ko: '무료 온라인 카드 제작기로 맞춤형 유희왕 카드를 만드세요. 몬스터, 마법, 함정, 펜듈럼, 링크 몬스터를 포함한 모든 카드 유형을 지원합니다.',
    pt: 'Crie cartas Yu-Gi-Oh! personalizadas com nosso criador de cartas online gratuito. Suporte para todos os tipos de cartas incluindo monstros, magias, armadilhas, pêndulo e monstros link.',
    es: 'Crea cartas Yu-Gi-Oh! personalizadas con nuestro creador de cartas en línea gratuito. Soporte para todos los tipos de cartas incluyendo monstruos, hechizos, trampas, péndulo y monstruos enlace.',
    el: 'Δημιουργήστε προσαρμοσμένες κάρτες Yu-Gi-Oh! με τον δωρεάν online δημιουργό καρτών μας. Υποστήριξη για όλους τους τύπους καρτών συμπεριλαμβανομένων τεράτων, ξόρκια, παγίδες, εκκρεμές και link τέρατα.',
    th: 'สร้างการ์ด Yu-Gi-Oh! แบบกำหนดเองด้วยเครื่องมือสร้างการ์ดออนไลน์ฟรีของเรา รองรับการ์ดทุกประเภทรวมถึงมอนสเตอร์ เวทมนตร์ กับดัก เพนดูลัม และลิงค์มอนสเตอร์',
    ru: 'Создавайте пользовательские карты Yu-Gi-Oh! с помощью нашего бесплатного онлайн-создателя карт. Поддержка всех типов карт, включая монстров, заклинания, ловушки, маятник и линк-монстров.',
    vi: 'Tạo thẻ Yu-Gi-Oh! tùy chỉnh với trình tạo thẻ trực tuyến miễn phí của chúng tôi. Hỗ trợ tất cả các loại thẻ bao gồm quái vật, phép thuật, bẫy, pendulum và link monster.'
  }

  const pageTitle = titles[locale] || titles.en
  const pageDescription = descriptions[locale] || descriptions.en

  // Clean path for canonical and hreflang generation
  let cleanPath = route.path
  if (locale !== 'en' && route.path.startsWith(`/${locale}`)) {
    cleanPath = route.path.replace(`/${locale}`, '') || '/'
  }

  // Generate canonical URL
  const canonicalUrl = locale === 'en'
    ? (cleanPath === '/' ? `${baseUrl}/` : `${baseUrl}${cleanPath}`)
    : (cleanPath === '/' ? `${baseUrl}/${locale}/` : `${baseUrl}/${locale}${cleanPath}`)

  // Update document title
  document.title = pageTitle

  // Update meta description
  updateMetaTag('name', 'description', pageDescription)
  
  // Update Open Graph tags
  updateMetaTag('property', 'og:title', pageTitle)
  updateMetaTag('property', 'og:description', pageDescription)
  updateMetaTag('property', 'og:url', canonicalUrl)
  
  // Update Twitter tags
  updateMetaTag('name', 'twitter:title', pageTitle)
  updateMetaTag('name', 'twitter:description', pageDescription)

  // Update canonical link
  updateLinkTag('canonical', canonicalUrl)

  // Update hreflang links
  updateHreflangLinks(cleanPath, baseUrl)

  // Update html lang attribute
  document.documentElement.lang = locale
}

function updateMetaTag(attribute, value, content) {
  let meta = document.querySelector(`meta[${attribute}="${value}"]`)
  if (!meta) {
    meta = document.createElement('meta')
    meta.setAttribute(attribute, value)
    document.head.appendChild(meta)
  }
  meta.setAttribute('content', content)
}

function updateLinkTag(rel, href) {
  let link = document.querySelector(`link[rel="${rel}"]`)
  if (!link) {
    link = document.createElement('link')
    link.setAttribute('rel', rel)
    document.head.appendChild(link)
  }
  link.setAttribute('href', href)
}

function updateHreflangLinks(cleanPath, baseUrl) {
  // Remove existing hreflang links
  const existingHreflangLinks = document.querySelectorAll('link[hreflang]')
  existingHreflangLinks.forEach(link => link.remove())

  const languages = ['en', 'zh', 'ja', 'de', 'fr', 'ko', 'pt', 'es', 'el', 'th', 'ru', 'vi']
  
  // Add hreflang links for each language
  languages.forEach(lang => {
    const href = lang === 'en'
      ? (cleanPath === '/' ? `${baseUrl}/` : `${baseUrl}${cleanPath}`)
      : (cleanPath === '/' ? `${baseUrl}/${lang}/` : `${baseUrl}/${lang}${cleanPath}`)
    
    const link = document.createElement('link')
    link.setAttribute('rel', 'alternate')
    link.setAttribute('hreflang', lang)
    link.setAttribute('href', href)
    document.head.appendChild(link)
  })

  // Add x-default hreflang
  const xDefaultHref = cleanPath === '/' ? `${baseUrl}/` : `${baseUrl}${cleanPath}`
  const xDefaultLink = document.createElement('link')
  xDefaultLink.setAttribute('rel', 'alternate')
  xDefaultLink.setAttribute('hreflang', 'x-default')
  xDefaultLink.setAttribute('href', xDefaultHref)
  document.head.appendChild(xDefaultLink)
}
