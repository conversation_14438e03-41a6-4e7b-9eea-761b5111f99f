#!/usr/bin/env node

/**
 * 性能测试脚本
 * 用于自动化测试网站首次加载性能
 */

const puppeteer = require('puppeteer')
const fs = require('fs')
const path = require('path')

class PerformanceTester {
  constructor(options = {}) {
    this.options = {
      url: options.url || 'http://localhost:3000',
      iterations: options.iterations || 3,
      headless: options.headless !== false,
      outputDir: options.outputDir || './performance-reports',
      ...options
    }
  }

  async runTests() {
    console.log('🚀 Starting performance tests...')
    console.log(`URL: ${this.options.url}`)
    console.log(`Iterations: ${this.options.iterations}`)
    
    // 确保输出目录存在
    if (!fs.existsSync(this.options.outputDir)) {
      fs.mkdirSync(this.options.outputDir, { recursive: true })
    }

    const results = []
    
    for (let i = 0; i < this.options.iterations; i++) {
      console.log(`\n📊 Running test ${i + 1}/${this.options.iterations}...`)
      
      try {
        const result = await this.runSingleTest()
        results.push(result)
        
        console.log(`✅ Test ${i + 1} completed`)
        console.log(`   - Load time: ${result.loadTime}ms`)
        console.log(`   - FCP: ${result.fcp}ms`)
        console.log(`   - LCP: ${result.lcp}ms`)
        
      } catch (error) {
        console.error(`❌ Test ${i + 1} failed:`, error.message)
      }
      
      // 等待一段时间再进行下一次测试
      if (i < this.options.iterations - 1) {
        await new Promise(resolve => setTimeout(resolve, 2000))
      }
    }

    // 生成报告
    const report = this.generateReport(results)
    await this.saveReport(report)
    
    console.log('\n📈 Performance test completed!')
    console.log(`📄 Report saved to: ${this.options.outputDir}`)
    
    return report
  }

  async runSingleTest() {
    const browser = await puppeteer.launch({
      headless: this.options.headless,
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    })

    try {
      const page = await browser.newPage()
      
      // 清除缓存
      await page.setCacheEnabled(false)
      
      // 设置视口
      await page.setViewport({ width: 1920, height: 1080 })
      
      // 开始性能监控
      const startTime = Date.now()
      
      // 导航到页面
      const response = await page.goto(this.options.url, {
        waitUntil: 'networkidle0',
        timeout: 30000
      })

      // 等待加载对话框消失
      await page.waitForFunction(
        () => !document.querySelector('.modal.show'),
        { timeout: 10000 }
      ).catch(() => {
        console.warn('Loading dialog timeout - continuing test')
      })

      // 等待Canvas元素出现并渲染
      await page.waitForSelector('#yugiohcard', { timeout: 10000 })
      
      // 等待一段时间确保渲染完成
      await page.waitForTimeout(1000)

      const endTime = Date.now()
      const loadTime = endTime - startTime

      // 获取性能指标
      const metrics = await page.evaluate(() => {
        const perfEntries = performance.getEntriesByType('navigation')[0]
        const paintEntries = performance.getEntriesByType('paint')
        
        const fcp = paintEntries.find(entry => entry.name === 'first-contentful-paint')
        const fp = paintEntries.find(entry => entry.name === 'first-paint')
        
        return {
          domContentLoaded: perfEntries.domContentLoadedEventEnd - perfEntries.navigationStart,
          loadComplete: perfEntries.loadEventEnd - perfEntries.navigationStart,
          fcp: fcp ? fcp.startTime : null,
          fp: fp ? fp.startTime : null,
          ttfb: perfEntries.responseStart - perfEntries.navigationStart
        }
      })

      // 获取LCP
      const lcp = await page.evaluate(() => {
        return new Promise((resolve) => {
          if ('PerformanceObserver' in window) {
            const observer = new PerformanceObserver((list) => {
              const entries = list.getEntries()
              const lastEntry = entries[entries.length - 1]
              resolve(lastEntry ? lastEntry.startTime : null)
            })
            
            try {
              observer.observe({ entryTypes: ['largest-contentful-paint'] })
              setTimeout(() => resolve(null), 5000) // 5秒超时
            } catch (e) {
              resolve(null)
            }
          } else {
            resolve(null)
          }
        })
      })

      return {
        timestamp: new Date().toISOString(),
        loadTime,
        fcp: metrics.fcp,
        lcp: lcp,
        fp: metrics.fp,
        domContentLoaded: metrics.domContentLoaded,
        loadComplete: metrics.loadComplete,
        ttfb: metrics.ttfb,
        url: this.options.url,
        userAgent: await page.evaluate(() => navigator.userAgent)
      }

    } finally {
      await browser.close()
    }
  }

  generateReport(results) {
    if (results.length === 0) {
      return { error: 'No successful test results' }
    }

    const validResults = results.filter(r => r && r.loadTime)
    
    if (validResults.length === 0) {
      return { error: 'No valid test results' }
    }

    const metrics = ['loadTime', 'fcp', 'lcp', 'domContentLoaded', 'loadComplete', 'ttfb']
    const summary = {}

    metrics.forEach(metric => {
      const values = validResults
        .map(r => r[metric])
        .filter(v => v !== null && v !== undefined)
      
      if (values.length > 0) {
        summary[metric] = {
          min: Math.min(...values),
          max: Math.max(...values),
          avg: Math.round(values.reduce((a, b) => a + b, 0) / values.length),
          median: this.calculateMedian(values)
        }
      }
    })

    // 性能评级
    const grade = this.calculateGrade(summary)

    return {
      timestamp: new Date().toISOString(),
      testConfig: this.options,
      summary,
      grade,
      results: validResults,
      recommendations: this.generateRecommendations(summary)
    }
  }

  calculateMedian(values) {
    const sorted = values.sort((a, b) => a - b)
    const mid = Math.floor(sorted.length / 2)
    return sorted.length % 2 !== 0 ? sorted[mid] : (sorted[mid - 1] + sorted[mid]) / 2
  }

  calculateGrade(summary) {
    let score = 100
    
    // FCP评分
    if (summary.fcp && summary.fcp.avg > 3000) score -= 30
    else if (summary.fcp && summary.fcp.avg > 1800) score -= 15
    
    // LCP评分
    if (summary.lcp && summary.lcp.avg > 4000) score -= 30
    else if (summary.lcp && summary.lcp.avg > 2500) score -= 15
    
    // 加载时间评分
    if (summary.loadTime && summary.loadTime.avg > 5000) score -= 25
    else if (summary.loadTime && summary.loadTime.avg > 3000) score -= 10
    
    if (score >= 90) return 'A'
    if (score >= 80) return 'B'
    if (score >= 70) return 'C'
    if (score >= 60) return 'D'
    return 'F'
  }

  generateRecommendations(summary) {
    const recommendations = []
    
    if (summary.fcp && summary.fcp.avg > 1800) {
      recommendations.push('考虑优化首次内容绘制时间，可以通过预加载关键资源来改善')
    }
    
    if (summary.lcp && summary.lcp.avg > 2500) {
      recommendations.push('最大内容绘制时间较长，建议优化图片加载和渲染性能')
    }
    
    if (summary.loadTime && summary.loadTime.avg > 3000) {
      recommendations.push('总加载时间较长，建议检查网络请求和资源大小')
    }
    
    if (summary.ttfb && summary.ttfb.avg > 600) {
      recommendations.push('服务器响应时间较长，建议优化服务器性能或使用CDN')
    }
    
    return recommendations
  }

  async saveReport(report) {
    const filename = `performance-report-${new Date().toISOString().replace(/[:.]/g, '-')}.json`
    const filepath = path.join(this.options.outputDir, filename)
    
    fs.writeFileSync(filepath, JSON.stringify(report, null, 2))
    
    // 也保存一个最新的报告
    const latestPath = path.join(this.options.outputDir, 'latest-report.json')
    fs.writeFileSync(latestPath, JSON.stringify(report, null, 2))
    
    console.log(`📄 Report saved: ${filepath}`)
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  const args = process.argv.slice(2)
  const options = {}
  
  // 解析命令行参数
  for (let i = 0; i < args.length; i += 2) {
    const key = args[i].replace('--', '')
    const value = args[i + 1]
    
    if (key === 'iterations') options.iterations = parseInt(value)
    else if (key === 'url') options.url = value
    else if (key === 'headless') options.headless = value !== 'false'
  }
  
  const tester = new PerformanceTester(options)
  tester.runTests().catch(console.error)
}

module.exports = PerformanceTester
