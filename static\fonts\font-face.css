/* 核心字体 - 优先加载 */
@font-face {
	font-family: "MatrixBoldSmallCaps";
	src:url("MatrixBoldSmallCaps.ttf") format("truetype");
	font-weight: bold;
	font-display: swap;
}

/* 英文字体 - 默认优先 */
@font-face {
	font-family: "en";
	src:url("en.ttf") format("truetype");
	font-display: swap;
}

/* 连接字体 - 高优先级 */
@font-face {
	font-family: "link";
	src:url("link.ttf") format("truetype");
	font-display: swap;
}

/* 中文字体 - 按需加载 */
@font-face {
	font-family: "zh";
	src:url("zh.ttf") format("truetype");
	font-display: optional;
}

@font-face {
	font-family: "cn";
	src:url("cn.ttf") format("truetype");
	font-display: optional;
}

/* 日文字体 - 按需加载 */
@font-face {
	font-family: "jp";
	src:url("jp.ttf") format("truetype");
	font-display: optional;
}

@font-face {
	font-family: "jp2";
	src:url("jp2.otf") format("opentype");
	font-display: optional;
}

/* 备用英文字体 - 延迟加载 */
@font-face {
	font-family: "en2";
	src:url("en2.ttf") format("truetype");
	font-display: optional;
}

@font-face {
	font-family: "en3";
	src:url("en3.ttf") format("truetype");
	font-display: optional;
}
