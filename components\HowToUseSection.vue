<template>
  <section class="how-to-use-section py-5" role="main">
    <div class="container">
      <!-- Section Header -->
      <header class="text-center mb-5">
        <h2 class="section-title display-5 font-weight-bold text-dark mb-3">
          {{ howToUseData.title }}
        </h2>
        <p class="section-subtitle lead text-muted mb-4">
          {{ howToUseData.subtitle }}
        </p>
        <div class="title-divider mx-auto"></div>
      </header>
      
      <!-- Steps -->
      <div class="row g-4">
        <div 
          v-for="(step, index) in howToUseData.steps" 
          :key="index"
          class="col-lg-4 col-md-6"
        >
          <article class="step-card h-100">
            <div class="step-number-wrapper">
              <div class="step-number">
                {{ step.step }}
              </div>
              <div v-if="index < howToUseData.steps.length - 1" class="step-connector"></div>
            </div>
            
            <div class="step-content">
              <h3 class="step-title h5 font-weight-bold mb-3">
                {{ step.title }}
              </h3>
              
              <p class="step-description text-muted mb-0">
                {{ step.description }}
              </p>
            </div>
          </article>
        </div>
      </div>
      
      <!-- Interactive Demo -->
      <div class="demo-section mt-5">
        <div class="row align-items-center">
          <div class="col-lg-6">
            <div class="demo-content">
              <h3 class="demo-title h4 font-weight-bold mb-3">
                {{ demoTitle }}
              </h3>
              <p class="demo-description text-muted mb-4">
                {{ demoDescription }}
              </p>
              <button 
                @click="scrollToEditor"
                class="btn btn-outline-primary btn-lg"
                :aria-label="tryNowText"
              >
                <fa :icon="['fas', 'play-circle']" class="me-2" />
                {{ tryNowText }}
              </button>
            </div>
          </div>
          
          <div class="col-lg-6">
            <div class="demo-visual">
              <div class="demo-card">
                <div class="demo-card-header">
                  <div class="demo-dots">
                    <span class="dot dot-red"></span>
                    <span class="dot dot-yellow"></span>
                    <span class="dot dot-green"></span>
                  </div>
                </div>
                <div class="demo-card-body">
                  <div class="demo-preview">
                    <div class="demo-card-image"></div>
                    <div class="demo-card-info">
                      <div class="demo-line demo-line-title"></div>
                      <div class="demo-line demo-line-short"></div>
                      <div class="demo-line demo-line-medium"></div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script>
import { mapGetters } from 'vuex'

export default {
  name: 'HowToUseSection',
  
  computed: {
    ...mapGetters(['currentUIData']),
    
    howToUseData() {
      return this.currentUIData.sections?.how_to_use || {
        title: '使用方法',
        subtitle: '三步輕鬆製作專屬卡片',
        steps: [
          {
            step: '1',
            title: '選擇卡片類型',
            description: '選擇怪獸卡、魔法卡或陷阱卡，並設置相應的屬性和效果。'
          },
          {
            step: '2',
            title: '自定義內容',
            description: '上傳卡片圖片，填寫卡片名稱、效果說明等詳細信息。'
          },
          {
            step: '3',
            title: '生成下載',
            description: '實時預覽效果，滿意後點擊下載按鈕獲取高質量卡片圖片。'
          }
        ]
      }
    },
    
    demoTitle() {
      return this.currentUIData.demo_title || '立即體驗'
    },
    
    demoDescription() {
      return this.currentUIData.demo_description || '無需註冊，立即開始製作你的專屬遊戲王卡片。'
    },
    
    tryNowText() {
      return this.currentUIData.try_now || '立即試用'
    }
  },
  
  methods: {
    /**
     * 滾動到編輯器區域
     */
    scrollToEditor() {
      const editorElement = document.getElementById('card-editor')
      if (editorElement) {
        editorElement.scrollIntoView({ 
          behavior: 'smooth',
          block: 'start'
        })
      }
    }
  }
}
</script>

<style scoped>
.how-to-use-section {
  background: white;
  position: relative;
}

.section-title {
  color: #2c3e50;
  font-weight: 700;
}

.section-subtitle {
  color: #6c757d;
  font-size: 1.1rem;
  max-width: 600px;
  margin: 0 auto;
}

.title-divider {
  width: 80px;
  height: 4px;
  background: linear-gradient(45deg, #007bff, #6610f2);
  border-radius: 2px;
}

.step-card {
  text-align: center;
  position: relative;
  padding: 2rem 1rem;
}

.step-number-wrapper {
  position: relative;
  margin-bottom: 2rem;
}

.step-number {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, #007bff, #6610f2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
  color: white;
  font-size: 2rem;
  font-weight: bold;
  box-shadow: 0 8px 25px rgba(0, 123, 255, 0.3);
  transition: all 0.3s ease;
  position: relative;
  z-index: 2;
}

.step-number::before {
  content: '';
  position: absolute;
  top: -5px;
  left: -5px;
  right: -5px;
  bottom: -5px;
  background: linear-gradient(135deg, #007bff, #6610f2);
  border-radius: 50%;
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: -1;
}

.step-card:hover .step-number {
  transform: scale(1.1);
}

.step-card:hover .step-number::before {
  opacity: 0.3;
}

.step-connector {
  position: absolute;
  top: 50%;
  left: calc(100% + 1rem);
  width: calc(100vw / 3 - 4rem);
  height: 2px;
  background: linear-gradient(90deg, #007bff, #6610f2);
  transform: translateY(-50%);
  z-index: 1;
}

.step-title {
  color: #2c3e50;
  font-weight: 600;
  margin-bottom: 1rem;
}

.step-description {
  color: #6c757d;
  line-height: 1.6;
}

.demo-section {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 20px;
  padding: 3rem 2rem;
  margin-top: 3rem;
}

.demo-title {
  color: #2c3e50;
  font-weight: 700;
}

.demo-description {
  color: #6c757d;
  font-size: 1.1rem;
  line-height: 1.6;
}

.btn-outline-primary {
  border: 2px solid #007bff;
  color: #007bff;
  font-weight: 600;
  padding: 1rem 2rem;
  border-radius: 50px;
  transition: all 0.3s ease;
}

.btn-outline-primary:hover {
  background: #007bff;
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 123, 255, 0.4);
}

.demo-visual {
  display: flex;
  justify-content: center;
  align-items: center;
}

.demo-card {
  background: white;
  border-radius: 15px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  width: 300px;
  transition: transform 0.3s ease;
}

.demo-card:hover {
  transform: translateY(-5px);
}

.demo-card-header {
  background: #f8f9fa;
  padding: 1rem;
  border-bottom: 1px solid #e9ecef;
}

.demo-dots {
  display: flex;
  gap: 0.5rem;
}

.dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
}

.dot-red { background: #ff5f56; }
.dot-yellow { background: #ffbd2e; }
.dot-green { background: #27ca3f; }

.demo-card-body {
  padding: 1.5rem;
}

.demo-preview {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.demo-card-image {
  width: 100%;
  height: 120px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 8px;
  position: relative;
}

.demo-card-image::before {
  content: '🎴';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 2rem;
}

.demo-card-info {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.demo-line {
  height: 8px;
  background: #e9ecef;
  border-radius: 4px;
  animation: pulse 2s infinite;
}

.demo-line-title {
  width: 80%;
  background: #007bff;
}

.demo-line-short {
  width: 60%;
}

.demo-line-medium {
  width: 70%;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.6; }
}

/* Responsive design */
@media (max-width: 992px) {
  .step-connector {
    display: none;
  }
  
  .demo-section {
    text-align: center;
  }
  
  .demo-visual {
    margin-top: 2rem;
  }
}

@media (max-width: 768px) {
  .step-number {
    width: 60px;
    height: 60px;
    font-size: 1.5rem;
  }
  
  .step-card {
    padding: 1.5rem 0.5rem;
  }
  
  .demo-section {
    padding: 2rem 1rem;
  }
  
  .demo-card {
    width: 250px;
  }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  .step-number,
  .demo-card,
  .btn-outline-primary,
  .demo-line {
    animation: none;
    transition: none;
  }
  
  .step-card:hover .step-number,
  .demo-card:hover,
  .btn-outline-primary:hover {
    transform: none;
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .step-number {
    background: #000;
    color: #fff;
    border: 2px solid #fff;
  }
  
  .demo-card {
    border: 2px solid #000;
  }
  
  .btn-outline-primary {
    border-color: #000;
    color: #000;
  }
  
  .btn-outline-primary:hover {
    background: #000;
    color: #fff;
  }
}
</style>
