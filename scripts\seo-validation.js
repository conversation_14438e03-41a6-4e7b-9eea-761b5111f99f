#!/usr/bin/env node

/**
 * SEO Validation Script for Yu-Gi-Oh! Card Maker
 * Validates SEO configuration across all pages
 */

const fs = require('fs')
const path = require('path')
const { JSDOM } = require('jsdom')

// Configuration
const config = {
  baseUrl: 'https://yugiohcardmaker.org',
  languages: ['en', 'zh', 'ja', 'de', 'fr', 'ko', 'pt', 'es', 'el', 'th', 'ru', 'vi'],
  defaultLanguage: 'en',
  distDir: path.join(__dirname, '..', 'dist'),
  gaId: 'G-PN1XZ4X9VG'
}

// Pages to validate
const pages = [
  { path: '/', name: 'Homepage' },
  { path: '/privacy', name: 'Privacy Policy' },
  { path: '/terms', name: 'Terms of Service' },
  { path: '/analytics-test', name: 'Analytics Test' }
]

/**
 * Validate SEO elements in HTML content
 */
function validateSEO(htmlContent, pagePath, language = 'en') {
  const dom = new JSDOM(htmlContent)
  const document = dom.window.document
  const results = {
    page: pagePath,
    language: language,
    issues: [],
    warnings: [],
    passed: []
  }

  // Check robots meta tag
  const robotsMeta = document.querySelector('meta[name="robots"]')
  if (!robotsMeta) {
    results.issues.push('Missing robots meta tag')
  } else {
    const robotsContent = robotsMeta.getAttribute('content')
    if (pagePath.includes('/analytics-test')) {
      if (!robotsContent.includes('noindex')) {
        results.warnings.push('Analytics test page should have noindex directive')
      } else {
        results.passed.push('Correct robots directive for test page')
      }
    } else {
      if (!robotsContent.includes('index')) {
        results.warnings.push('Page should allow indexing')
      } else {
        results.passed.push('Correct robots directive for public page')
      }
    }
  }

  // Check canonical URL
  const canonical = document.querySelector('link[rel="canonical"]')
  if (!canonical) {
    results.issues.push('Missing canonical URL')
  } else {
    const canonicalHref = canonical.getAttribute('href')
    const expectedCanonical = language === 'en' 
      ? `${config.baseUrl}${pagePath === '/' ? '/' : pagePath}`
      : `${config.baseUrl}/${language}${pagePath === '/' ? '/' : pagePath}`
    
    if (canonicalHref !== expectedCanonical) {
      results.warnings.push(`Canonical URL mismatch. Expected: ${expectedCanonical}, Found: ${canonicalHref}`)
    } else {
      results.passed.push('Correct canonical URL')
    }
  }

  // Check hreflang tags
  const hreflangLinks = document.querySelectorAll('link[rel="alternate"][hreflang]')
  if (hreflangLinks.length === 0) {
    results.issues.push('Missing hreflang tags')
  } else {
    const expectedHreflangCount = config.languages.length + 1 // +1 for x-default
    if (hreflangLinks.length < expectedHreflangCount) {
      results.warnings.push(`Incomplete hreflang tags. Expected: ${expectedHreflangCount}, Found: ${hreflangLinks.length}`)
    } else {
      results.passed.push('Complete hreflang tags')
    }
  }

  // Check Google Analytics
  const gaScript = document.querySelector(`script[src*="googletagmanager.com/gtag/js?id=${config.gaId}"]`)
  const gaConfig = document.querySelector('script[innerHTML*="gtag"]') || 
                   Array.from(document.querySelectorAll('script')).find(script => 
                     script.innerHTML && script.innerHTML.includes('gtag'))
  
  if (!gaScript) {
    results.issues.push('Missing Google Analytics script')
  } else {
    results.passed.push('Google Analytics script found')
  }

  if (!gaConfig) {
    results.issues.push('Missing Google Analytics configuration')
  } else {
    if (gaConfig.innerHTML.includes(config.gaId)) {
      results.passed.push('Correct Google Analytics tracking ID')
    } else {
      results.warnings.push('Google Analytics tracking ID not found in configuration')
    }
  }

  // Check meta description
  const description = document.querySelector('meta[name="description"]')
  if (!description) {
    results.issues.push('Missing meta description')
  } else {
    const descContent = description.getAttribute('content')
    if (!descContent || descContent.length < 120) {
      results.warnings.push('Meta description too short (should be 120-160 characters)')
    } else if (descContent.length > 160) {
      results.warnings.push('Meta description too long (should be 120-160 characters)')
    } else {
      results.passed.push('Good meta description length')
    }
  }

  // Check Open Graph tags
  const ogTitle = document.querySelector('meta[property="og:title"]')
  const ogDescription = document.querySelector('meta[property="og:description"]')
  const ogUrl = document.querySelector('meta[property="og:url"]')
  
  if (!ogTitle) results.warnings.push('Missing og:title')
  if (!ogDescription) results.warnings.push('Missing og:description')
  if (!ogUrl) results.warnings.push('Missing og:url')
  
  if (ogTitle && ogDescription && ogUrl) {
    results.passed.push('Complete Open Graph tags')
  }

  // Check structured data
  const structuredData = document.querySelector('script[type="application/ld+json"]')
  if (!structuredData) {
    results.warnings.push('Missing structured data')
  } else {
    try {
      JSON.parse(structuredData.innerHTML)
      results.passed.push('Valid structured data found')
    } catch (e) {
      results.issues.push('Invalid structured data JSON')
    }
  }

  return results
}

/**
 * Find HTML files in dist directory
 */
function findHtmlFiles(dir, files = []) {
  const items = fs.readdirSync(dir)
  
  for (const item of items) {
    const fullPath = path.join(dir, item)
    const stat = fs.statSync(fullPath)
    
    if (stat.isDirectory()) {
      findHtmlFiles(fullPath, files)
    } else if (item === 'index.html') {
      files.push(fullPath)
    }
  }
  
  return files
}

/**
 * Main validation function
 */
function validateAllPages() {
  console.log('🔍 Starting SEO validation...\n')
  
  if (!fs.existsSync(config.distDir)) {
    console.error('❌ Dist directory not found. Please run build first.')
    process.exit(1)
  }

  const htmlFiles = findHtmlFiles(config.distDir)
  const allResults = []

  for (const filePath of htmlFiles) {
    const htmlContent = fs.readFileSync(filePath, 'utf8')
    const relativePath = path.relative(config.distDir, filePath)
    
    // Determine page path and language from file structure
    let pagePath = '/'
    let language = 'en'
    
    if (relativePath.includes('/')) {
      const parts = relativePath.split('/')
      if (config.languages.includes(parts[0])) {
        language = parts[0]
        pagePath = parts.length > 2 ? `/${parts.slice(1, -1).join('/')}` : '/'
      } else {
        pagePath = parts.length > 1 ? `/${parts.slice(0, -1).join('/')}` : '/'
      }
    }

    const results = validateSEO(htmlContent, pagePath, language)
    allResults.push(results)
  }

  // Print results
  let totalIssues = 0
  let totalWarnings = 0
  let totalPassed = 0

  for (const result of allResults) {
    console.log(`📄 ${result.page} (${result.language})`)
    
    if (result.issues.length > 0) {
      console.log('  ❌ Issues:')
      result.issues.forEach(issue => console.log(`    - ${issue}`))
    }
    
    if (result.warnings.length > 0) {
      console.log('  ⚠️  Warnings:')
      result.warnings.forEach(warning => console.log(`    - ${warning}`))
    }
    
    if (result.passed.length > 0) {
      console.log('  ✅ Passed:')
      result.passed.forEach(passed => console.log(`    - ${passed}`))
    }
    
    totalIssues += result.issues.length
    totalWarnings += result.warnings.length
    totalPassed += result.passed.length
    
    console.log('')
  }

  // Summary
  console.log('📊 Summary:')
  console.log(`  ✅ Passed: ${totalPassed}`)
  console.log(`  ⚠️  Warnings: ${totalWarnings}`)
  console.log(`  ❌ Issues: ${totalIssues}`)
  
  if (totalIssues > 0) {
    console.log('\n❌ SEO validation failed. Please fix the issues above.')
    process.exit(1)
  } else if (totalWarnings > 0) {
    console.log('\n⚠️  SEO validation passed with warnings. Consider addressing them.')
  } else {
    console.log('\n🎉 SEO validation passed successfully!')
  }
}

// Run validation
if (require.main === module) {
  validateAllPages()
}

module.exports = { validateSEO, validateAllPages }
