/**
 * 图片预加载工具类
 * 用于优化首次加载性能，实现智能的图片预加载策略
 */

export class ImagePreloader {
  constructor() {
    this.cache = new Map()
    this.loadingPromises = new Map()
    this.preloadQueue = []
    this.isPreloading = false
  }

  /**
   * 预加载单个图片
   * @param {string} src - 图片URL
   * @param {Object} options - 配置选项
   * @returns {Promise<HTMLImageElement>}
   */
  preloadImage(src, options = {}) {
    const { priority = 'normal', timeout = 10000 } = options

    // 如果已经在缓存中，直接返回
    if (this.cache.has(src)) {
      return Promise.resolve(this.cache.get(src))
    }

    // 如果正在加载，返回现有的Promise
    if (this.loadingPromises.has(src)) {
      return this.loadingPromises.get(src)
    }

    // 创建新的加载Promise
    const loadPromise = new Promise((resolve, reject) => {
      const img = new Image()
      
      // 设置超时
      const timeoutId = setTimeout(() => {
        reject(new Error(`Image load timeout: ${src}`))
      }, timeout)

      img.onload = () => {
        clearTimeout(timeoutId)
        this.cache.set(src, img)
        this.loadingPromises.delete(src)
        resolve(img)
      }

      img.onerror = () => {
        clearTimeout(timeoutId)
        this.loadingPromises.delete(src)
        reject(new Error(`Failed to load image: ${src}`))
      }

      img.src = src
    })

    this.loadingPromises.set(src, loadPromise)
    return loadPromise
  }

  /**
   * 批量预加载图片
   * @param {Array<string|Object>} images - 图片URL数组或配置对象数组
   * @param {Object} options - 全局配置选项
   * @returns {Promise<Array>}
   */
  async preloadImages(images, options = {}) {
    const { concurrency = 3, failFast = false } = options

    const imageConfigs = images.map(img => 
      typeof img === 'string' ? { src: img } : img
    )

    if (failFast) {
      // 快速失败模式：任何一个失败就停止
      return Promise.all(
        imageConfigs.map(config => this.preloadImage(config.src, config))
      )
    } else {
      // 容错模式：继续加载其他图片
      return this.preloadWithConcurrency(imageConfigs, concurrency)
    }
  }

  /**
   * 并发控制的图片预加载
   * @param {Array} imageConfigs - 图片配置数组
   * @param {number} concurrency - 并发数
   * @returns {Promise<Array>}
   */
  async preloadWithConcurrency(imageConfigs, concurrency) {
    const results = []
    const executing = []

    for (const config of imageConfigs) {
      const promise = this.preloadImage(config.src, config)
        .then(img => ({ status: 'fulfilled', value: img, src: config.src }))
        .catch(error => ({ status: 'rejected', reason: error, src: config.src }))

      results.push(promise)

      if (imageConfigs.length >= concurrency) {
        executing.push(promise)

        if (executing.length >= concurrency) {
          await Promise.race(executing)
          executing.splice(executing.findIndex(p => p === promise), 1)
        }
      }
    }

    return Promise.all(results)
  }

  /**
   * 获取关键图片列表（用于首次加载）
   * @param {string} language - 当前语言
   * @param {string} cardType - 卡片类型
   * @returns {Array<Object>}
   */
  getCriticalImages(language = 'en', cardType = 'Monster') {
    const basePath = '/images'
    
    return [
      // 默认卡片模板
      { src: `${basePath}/card/${language}/Normal.png`, priority: 'high' },
      
      // 默认属性图标
      { src: `${basePath}/attr/${language}/LIGHT.webp`, priority: 'high' },
      
      // 默认卡片图片
      { src: `${basePath}/default.jpg`, priority: 'high' },
      
      // 防伪贴
      { src: `${basePath}/pic/holo.png`, priority: 'medium' },
      
      // 等级/阶级图标
      { src: `${basePath}/pic/Level.webp`, priority: 'medium' },
      
      // 常用卡片类型模板
      { src: `${basePath}/card/${language}/Effect.png`, priority: 'low' },
      { src: `${basePath}/card/${language}/Spell.png`, priority: 'low' },
      { src: `${basePath}/card/${language}/Trap.png`, priority: 'low' }
    ]
  }

  /**
   * 预加载关键图片
   * @param {string} language - 当前语言
   * @param {string} cardType - 卡片类型
   * @returns {Promise<Array>}
   */
  async preloadCriticalImages(language = 'en', cardType = 'Monster') {
    const criticalImages = this.getCriticalImages(language, cardType)
    
    // 按优先级分组
    const highPriority = criticalImages.filter(img => img.priority === 'high')
    const mediumPriority = criticalImages.filter(img => img.priority === 'medium')
    const lowPriority = criticalImages.filter(img => img.priority === 'low')

    try {
      // 先加载高优先级图片
      await this.preloadImages(highPriority, { concurrency: 2, failFast: false })
      
      // 然后加载中优先级图片
      this.preloadImages(mediumPriority, { concurrency: 2, failFast: false })
      
      // 最后在后台加载低优先级图片
      setTimeout(() => {
        this.preloadImages(lowPriority, { concurrency: 1, failFast: false })
      }, 100)

      return true
    } catch (error) {
      console.warn('Critical image preload failed:', error)
      return false
    }
  }

  /**
   * 清理缓存
   * @param {number} maxAge - 最大缓存时间（毫秒）
   */
  clearCache(maxAge = 300000) { // 默认5分钟
    const now = Date.now()
    for (const [src, data] of this.cache.entries()) {
      if (data.timestamp && now - data.timestamp > maxAge) {
        this.cache.delete(src)
      }
    }
  }

  /**
   * 获取缓存状态
   * @returns {Object}
   */
  getCacheStats() {
    return {
      cacheSize: this.cache.size,
      loadingCount: this.loadingPromises.size,
      queueSize: this.preloadQueue.length
    }
  }
}

// 创建全局实例
export const imagePreloader = new ImagePreloader()

// 默认导出
export default imagePreloader
