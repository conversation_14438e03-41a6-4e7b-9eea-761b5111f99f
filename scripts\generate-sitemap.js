/**
 * Advanced Sitemap Generator for Yu-Gi-Oh! Card Maker
 * Generates XML sitemap with multi-language support and proper formatting
 */

const fs = require('fs')
const path = require('path')

// Configuration
const config = {
  hostname: 'https://yugiohcardmaker.org',
  languages: ['en', 'zh', 'ja', 'de', 'fr', 'ko', 'pt', 'es', 'el', 'th', 'ru', 'vi'], // 包含所有支持的语言
  defaultLanguage: 'en', // 与 nuxt.config.js 的 defaultLocale 保持一致
  outputPath: path.join(__dirname, '../dist/sitemap.xml'),
  staticOutputPath: path.join(__dirname, '../static/sitemap.xml')
}

// Page definitions with metadata - 移除 privacy 和 terms 页面
const pages = [
  {
    path: '/',
    priority: 1.0,
    changefreq: 'weekly',
    multilang: true,
    description: 'Yu-<PERSON>i-<PERSON>! Card Maker - Professional Card Creator'
  }
]

/**
 * Generate XML sitemap with proper formatting
 */
function generateSitemap() {
  const currentDate = new Date().toISOString()
  
  let xml = '<?xml version="1.0" encoding="UTF-8"?>\n'
  xml += '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9"\n'
  xml += '        xmlns:xhtml="http://www.w3.org/1999/xhtml">\n'

  pages.forEach(page => {
    // 为每种语言生成单独的 URL 条目
    config.languages.forEach(lang => {
      xml += '  <url>\n'

      // 根据 Nuxt.js i18n 策略生成正确的 URL
      // strategy: 'prefix_except_default' 意味着默认语言不需要前缀
      let url
      if (lang === config.defaultLanguage) {
        url = `${config.hostname}${page.path}`
      } else {
        // 非默认语言使用前缀
        url = page.path === '/'
          ? `${config.hostname}/${lang}/`
          : `${config.hostname}/${lang}${page.path}`
      }

      xml += `    <loc>${url}</loc>\n`
      xml += `    <lastmod>${currentDate}</lastmod>\n`
      xml += `    <changefreq>${page.changefreq}</changefreq>\n`
      xml += `    <priority>${page.priority}</priority>\n`

      // 添加 hreflang 标签指向所有语言版本
      if (page.multilang) {
        config.languages.forEach(hrefLang => {
          let hrefUrl
          if (hrefLang === config.defaultLanguage) {
            hrefUrl = `${config.hostname}${page.path}`
          } else {
            hrefUrl = page.path === '/'
              ? `${config.hostname}/${hrefLang}/`
              : `${config.hostname}/${hrefLang}${page.path}`
          }
          xml += `    <xhtml:link rel="alternate" hreflang="${hrefLang}" href="${hrefUrl}" />\n`
        })

        // 添加 x-default 指向默认语言
        xml += `    <xhtml:link rel="alternate" hreflang="x-default" href="${config.hostname}${page.path}" />\n`
      }

      xml += '  </url>\n'
    })
  })

  xml += '</urlset>\n'
  
  return xml
}

/**
 * Generate sitemap index for multiple sitemaps (future expansion)
 */
function generateSitemapIndex() {
  const currentDate = new Date().toISOString()
  
  let xml = '<?xml version="1.0" encoding="UTF-8"?>\n'
  xml += '<sitemapindex xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">\n'
  xml += '  <sitemap>\n'
  xml += `    <loc>${config.hostname}/sitemap.xml</loc>\n`
  xml += `    <lastmod>${currentDate}</lastmod>\n`
  xml += '  </sitemap>\n'
  xml += '</sitemapindex>\n'
  
  return xml
}

/**
 * Write sitemap to file
 */
function writeSitemap() {
  try {
    const sitemapXml = generateSitemap()

    // Write to static directory (for development)
    const staticDir = path.dirname(config.staticOutputPath)
    if (!fs.existsSync(staticDir)) {
      fs.mkdirSync(staticDir, { recursive: true })
    }
    fs.writeFileSync(config.staticOutputPath, sitemapXml, 'utf8')
    console.log(`✅ Sitemap generated successfully: ${config.staticOutputPath}`)

    // Write to dist directory (for production) if it exists
    const distDir = path.dirname(config.outputPath)
    if (fs.existsSync(distDir)) {
      fs.writeFileSync(config.outputPath, sitemapXml, 'utf8')
      console.log(`✅ Sitemap generated successfully: ${config.outputPath}`)
    }

    // Generate sitemap index (for future use)
    const indexPath = path.join(__dirname, '../static/sitemap-index.xml')
    const indexXml = generateSitemapIndex()
    fs.writeFileSync(indexPath, indexXml, 'utf8')
    console.log(`✅ Sitemap index generated: ${indexPath}`)
    
    // Log sitemap contents
    console.log('\n📋 Sitemap Contents:')
    pages.forEach(page => {
      console.log(`\n📄 Page: ${page.path} (Priority: ${page.priority}, Changefreq: ${page.changefreq})`)
      if (page.multilang) {
        config.languages.forEach(lang => {
          let langUrl
          if (lang === config.defaultLanguage) {
            langUrl = `${config.hostname}${page.path}`
          } else {
            langUrl = page.path === '/'
              ? `${config.hostname}/${lang}/`
              : `${config.hostname}/${lang}${page.path}`
          }
          console.log(`   └─ ${lang}: ${langUrl}`)
        })
      }
    })

    const totalUrls = pages.reduce((count, page) => {
      return count + (page.multilang ? config.languages.length : 1)
    }, 0)

    console.log(`\n📊 Total URLs: ${totalUrls}`)
    console.log(`📄 Total Pages: ${pages.length}`)
    console.log(`🌐 Languages: ${config.languages.join(', ')}`)
    console.log(`🔗 Hostname: ${config.hostname}`)
    console.log(`🚫 Excluded: privacy, terms pages`)
    
  } catch (error) {
    console.error('❌ Error generating sitemap:', error)
    process.exit(1)
  }
}

// Generate sitemap if run directly
if (require.main === module) {
  writeSitemap()
}

module.exports = {
  generateSitemap,
  generateSitemapIndex,
  writeSitemap,
  config,
  pages
}
