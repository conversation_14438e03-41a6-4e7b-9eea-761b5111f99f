/**
 * Google Analytics 4 (GA4) Plugin for Nuxt.js
 * Tracking ID: G-PN1XZ4X9VG
 * 
 * This plugin ensures Google Analytics works on all pages
 * and provides enhanced tracking capabilities
 */

export default ({ app, router, $config }) => {
  // Only run on client-side
  if (process.client && router) {
    // Get GA ID from runtime config or fallback to default
    const gaId = $config.gaId || 'G-PN1XZ4X9VG'
    const baseUrl = $config.baseURL || 'https://yugiohcardmaker.org'

    // Initialize Google Analytics
    const initGA = () => {
      // Create gtag function if it doesn't exist
      window.dataLayer = window.dataLayer || []
      window.gtag = function() {
        window.dataLayer.push(arguments)
      }

      // Initialize with current date
      window.gtag('js', new Date())

      // Configure GA4 with enhanced settings
      window.gtag('config', gaId, {
        page_title: document.title,
        page_location: window.location.href,
        custom_map: {
          'custom_parameter': 'yugioh_card_maker'
        },
        // Enhanced ecommerce and user engagement
        send_page_view: true,
        anonymize_ip: true,
        allow_google_signals: true,
        allow_ad_personalization_signals: false
      })

      console.log('Google Analytics initialized with tracking ID:', gaId)
    }

    // Track page views on route changes
    const trackPageView = (to) => {
      if (typeof window.gtag !== 'undefined') {
        window.gtag('config', gaId, {
          page_path: to.fullPath,
          page_title: document.title,
          page_location: window.location.href
        })
        
        // Send page view event
        window.gtag('event', 'page_view', {
          page_title: document.title,
          page_location: window.location.href,
          page_path: to.fullPath
        })

        console.log('GA4 Page view tracked:', to.fullPath)
      }
    }

    // Track custom events for card maker functionality
    const trackCardMakerEvents = () => {
      // Track card generation
      window.trackCardGeneration = (cardType, cardLang) => {
        if (typeof window.gtag !== 'undefined') {
          window.gtag('event', 'card_generated', {
            event_category: 'card_maker',
            event_label: `${cardType}_${cardLang}`,
            custom_parameter: 'card_creation'
          })
        }
      }

      // Track card download
      window.trackCardDownload = (format) => {
        if (typeof window.gtag !== 'undefined') {
          window.gtag('event', 'card_downloaded', {
            event_category: 'card_maker',
            event_label: format,
            custom_parameter: 'card_download'
          })
        }
      }

      // Track language change
      window.trackLanguageChange = (newLang, oldLang) => {
        if (typeof window.gtag !== 'undefined') {
          window.gtag('event', 'language_changed', {
            event_category: 'user_interaction',
            event_label: `${oldLang}_to_${newLang}`,
            custom_parameter: 'language_switch'
          })
        }
      }

      // Track card template change
      window.trackTemplateChange = (templateType) => {
        if (typeof window.gtag !== 'undefined') {
          window.gtag('event', 'template_changed', {
            event_category: 'card_maker',
            event_label: templateType,
            custom_parameter: 'template_selection'
          })
        }
      }
    }

    // Initialize GA when DOM is ready
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => {
        initGA()
        trackCardMakerEvents()
      })
    } else {
      initGA()
      trackCardMakerEvents()
    }

    // Track route changes
    router.afterEach((to, from) => {
      // Wait a bit for the page to load
      setTimeout(() => {
        trackPageView(to)
      }, 100)
    })

    // Track initial page load
    router.onReady(() => {
      trackPageView(router.currentRoute)
    })
  }
}
