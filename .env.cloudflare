# Cloudflare Pages Environment Configuration
# This file contains environment-specific settings for Cloudflare Pages deployment

# Base URL for the application
NUXT_ENV_BASE_URL=https://yugiohcardmaker.org

# Google Analytics Tracking ID
NUXT_ENV_GA_ID=G-PN1XZ4X9VG

# Node.js environment
NODE_ENV=production

# Build optimization flags
NODE_OPTIONS=--openssl-legacy-provider --max-old-space-size=4096

# Deployment environment identifier
DEPLOY_ENV=CLOUDFLARE_PAGES

# Enable static generation
NUXT_ENV_STATIC=true

# CDN optimization
NUXT_ENV_CDN_URL=https://yugiohcardmaker.org
