#!/usr/bin/env node

/**
 * Cloudflare Pages Optimization Script
 * This script optimizes the generated static files for Cloudflare Pages deployment
 */

const fs = require('fs')
const path = require('path')

const DIST_DIR = path.join(__dirname, '..', 'dist')

console.log('🚀 Starting Cloudflare Pages optimization...')

/**
 * Ensure _redirects file is in the correct location
 */
function ensureRedirectsFile() {
  const sourceRedirects = path.join(__dirname, '..', 'static', '_redirects')
  const targetRedirects = path.join(DIST_DIR, '_redirects')
  
  if (fs.existsSync(sourceRedirects)) {
    fs.copyFileSync(sourceRedirects, targetRedirects)
    console.log('✅ _redirects file copied to dist directory')
  } else {
    console.warn('⚠️  _redirects file not found in static directory')
  }
}

/**
 * Optimize HTML files for better performance
 */
function optimizeHtmlFiles() {
  const htmlFiles = []
  
  function findHtmlFiles(dir) {
    const files = fs.readdirSync(dir)
    
    for (const file of files) {
      const filePath = path.join(dir, file)
      const stat = fs.statSync(filePath)
      
      if (stat.isDirectory()) {
        findHtmlFiles(filePath)
      } else if (file.endsWith('.html')) {
        htmlFiles.push(filePath)
      }
    }
  }
  
  findHtmlFiles(DIST_DIR)
  
  htmlFiles.forEach(filePath => {
    let content = fs.readFileSync(filePath, 'utf8')
    
    // Add Cloudflare-specific optimizations
    content = content.replace(
      '<head>',
      '<head>\n  <meta name="cf-2fa-verify" content="false">'
    )
    
    // Optimize resource hints for Cloudflare
    content = content.replace(
      /<link rel="preconnect" href="https:\/\/fonts\.googleapis\.com">/g,
      '<link rel="preconnect" href="https://fonts.googleapis.com" crossorigin>'
    )
    
    fs.writeFileSync(filePath, content)
  })
  
  console.log(`✅ Optimized ${htmlFiles.length} HTML files`)
}

/**
 * Validate critical files exist
 */
function validateCriticalFiles() {
  const criticalFiles = [
    'index.html',
    'robots.txt',
    'sitemap.xml',
    '_redirects'
  ]
  
  const missingFiles = []
  
  criticalFiles.forEach(file => {
    const filePath = path.join(DIST_DIR, file)
    if (!fs.existsSync(filePath)) {
      missingFiles.push(file)
    }
  })
  
  if (missingFiles.length > 0) {
    console.error('❌ Missing critical files:', missingFiles)
    process.exit(1)
  } else {
    console.log('✅ All critical files present')
  }
}

/**
 * Generate deployment info file
 */
function generateDeploymentInfo() {
  const deploymentInfo = {
    buildTime: new Date().toISOString(),
    environment: 'cloudflare-pages',
    version: require('../package.json').version,
    nodeVersion: process.version,
    platform: 'static'
  }
  
  fs.writeFileSync(
    path.join(DIST_DIR, 'deployment-info.json'),
    JSON.stringify(deploymentInfo, null, 2)
  )
  
  console.log('✅ Deployment info generated')
}

/**
 * Optimize static assets
 */
function optimizeStaticAssets() {
  // Ensure proper MIME types for fonts
  const fontExtensions = ['.ttf', '.otf', '.woff', '.woff2']
  const fontsDir = path.join(DIST_DIR, 'fonts')
  
  if (fs.existsSync(fontsDir)) {
    const fontFiles = fs.readdirSync(fontsDir)
    console.log(`✅ Found ${fontFiles.length} font files`)
  }
  
  // Validate image assets
  const imagesDir = path.join(DIST_DIR, 'images')
  if (fs.existsSync(imagesDir)) {
    const imageFiles = fs.readdirSync(imagesDir, { recursive: true })
    console.log(`✅ Found ${imageFiles.length} image assets`)
  }
}

/**
 * Main optimization function
 */
function main() {
  try {
    ensureRedirectsFile()
    optimizeHtmlFiles()
    validateCriticalFiles()
    generateDeploymentInfo()
    optimizeStaticAssets()
    
    console.log('🎉 Cloudflare Pages optimization completed successfully!')
  } catch (error) {
    console.error('❌ Optimization failed:', error.message)
    process.exit(1)
  }
}

// Run the optimization
main()
