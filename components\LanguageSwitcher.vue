<template>
  <div class="language-switcher">
    <b-dropdown 
      :text="currentLanguageDisplay" 
      variant="outline-light" 
      size="sm"
      right
      class="language-dropdown"
    >
      <template #button-content>
        <fa :icon="['fas', 'globe']" class="me-2" />
        {{ currentLanguageDisplay }}
      </template>
      
      <b-dropdown-item
        v-for="lang in sortedLanguages"
        :key="lang.code"
        :active="currentLanguage === lang.code"
        @click="switchLanguage(lang.code)"
        class="language-option"
      >
        {{ lang.name }}
      </b-dropdown-item>
    </b-dropdown>
  </div>
</template>

<script>
import { mapGetters, mapMutations } from 'vuex'

export default {
  name: 'LanguageSwitcher',
  
  computed: {
    ...mapGetters(['currentLanguage', 'availableLanguages']),

    currentLanguageDisplay() {
      const current = this.availableLanguages.find(lang => lang.code === this.currentLanguage)
      return current ? current.shortName : 'Language'
    },

    sortedLanguages() {
      // 使用store中已经排好序的语言列表
      // 主要语言（英语、日语、中文）已经在store中排在前面
      return this.availableLanguages
    }
  },
  
  methods: {
    ...mapMutations(['setLanguage']),
    
    /**
     * 切換語言
     * @param {string} langCode - 語言代碼 (zh, en, ja)
     */
    async switchLanguage(langCode) {
      if (langCode !== this.currentLanguage) {
        try {
          // 如果支持 i18n 路由，使用路由切换
          if (this.$i18n && this.switchLocalePath) {
            await this.$router.push(this.switchLocalePath(langCode))
          }

          // 更新 Vuex store
          this.setLanguage(langCode)

          // 觸發全局語言切換事件
          this.$emit('language-changed', langCode)

          // 更新頁面標題和 meta 標籤
          this.updatePageMeta(langCode)

          // 保存到 localStorage
          if (typeof window !== 'undefined') {
            localStorage.setItem('preferred-language', langCode)
          }
        } catch (error) {
          console.error('Error switching language:', error)
        }
      }
    },
    
    /**
     * 更新頁面 meta 信息
     * @param {string} langCode - 語言代碼
     */
    updatePageMeta(langCode) {
      const langData = this.$store.getters.getLanguageData(langCode)
      
      if (langData && langData.seo) {
        // 更新頁面標題
        document.title = langData.seo.title
        
        // 更新 meta description
        const metaDescription = document.querySelector('meta[name="description"]')
        if (metaDescription) {
          metaDescription.setAttribute('content', langData.seo.description)
        }
        
        // 更新 html lang 屬性
        document.documentElement.setAttribute('lang', langCode)
        
        // 更新 Open Graph 標籤
        const ogTitle = document.querySelector('meta[property="og:title"]')
        const ogDescription = document.querySelector('meta[property="og:description"]')
        
        if (ogTitle) ogTitle.setAttribute('content', langData.seo.title)
        if (ogDescription) ogDescription.setAttribute('content', langData.seo.description)
      }
    }
  },
  
  mounted() {
    // 從 localStorage 恢復語言設置
    if (typeof window !== 'undefined') {
      const savedLanguage = localStorage.getItem('preferred-language')
      if (savedLanguage && this.availableLanguages.find(lang => lang.code === savedLanguage)) {
        this.switchLanguage(savedLanguage)
      }
    }
  }
}
</script>

<style scoped>
.language-switcher {
  position: relative;
}

.language-dropdown >>> .btn {
  border: 1px solid rgba(255, 255, 255, 0.3);
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.language-dropdown >>> .btn:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.5);
}

.language-option {
  display: flex;
  align-items: center;
  padding: 0.5rem 1rem;
  transition: background-color 0.2s ease;
}

.language-option:hover {
  background-color: rgba(0, 123, 255, 0.1);
}

.language-option.active {
  background-color: rgba(0, 123, 255, 0.2);
  font-weight: 600;
}



/* 響應式設計 */
@media (max-width: 768px) {
  .language-dropdown >>> .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
  }
}

/* 無障礙設計 */
.language-option:focus {
  outline: 2px solid #007bff;
  outline-offset: -2px;
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .language-dropdown >>> .btn {
    border-color: rgba(255, 255, 255, 0.2);
    background: rgba(0, 0, 0, 0.3);
  }
  
  .language-dropdown >>> .btn:hover {
    background: rgba(0, 0, 0, 0.5);
  }
}
</style>
