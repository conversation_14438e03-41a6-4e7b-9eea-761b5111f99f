// Google Analytics configuration - Environment-aware setup
const gaId = process.env.NUXT_ENV_GA_ID || 'G-PN1XZ4X9VG'
const baseUrl = process.env.NUXT_ENV_BASE_URL || 'https://yugiohcardmaker.org'

const gaTags = [
  {
    hid: 'gtag-script',
    src: `https://www.googletagmanager.com/gtag/js?id=${gaId}`,
    async: true
  },
  {
    hid: 'gtag-config',
    innerHTML: `
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());
      gtag('config', '${gaId}', {
        page_title: document.title,
        page_location: window.location.href,
        custom_map: {'custom_parameter': 'yugioh_card_maker'}
      });
    `,
    type: 'text/javascript',
    charset: 'utf-8'
  }
]

export default {
  // Disable server-side rendering: https://go.nuxtjs.dev/ssr-mode
  ssr: false,

  // Target: https://go.nuxtjs.dev/config-target
  target: 'static',

  // Environment-specific configuration
  publicRuntimeConfig: {
    baseURL: process.env.NUXT_ENV_BASE_URL || 'https://yugiohcardmaker.org',
    gaId: process.env.NUXT_ENV_GA_ID || 'G-PN1XZ4X9VG',
    cdnUrl: process.env.NUXT_ENV_CDN_URL || 'https://yugiohcardmaker.org'
  },

  privateRuntimeConfig: {
    // Private keys which are only available on server-side
  },

  // Global page headers: https://go.nuxtjs.dev/config-head
  head: {
    // Default title will be overridden by i18n
    title: 'Yu-Gi-Oh! Card Maker',
    titleTemplate: '%s',
    htmlAttrs: {
      lang: 'en'
    },
    meta: [
      // Basic meta tags
      { charset: 'utf-8' },
      { name: 'viewport', content: 'width=device-width, initial-scale=1, shrink-to-fit=no' },
      { name: 'format-detection', content: 'telephone=no' },
      { name: 'theme-color', content: '#2c3e50' },

      // Google AdSense meta tag
      { name: 'google-adsense-account', content: 'ca-pub-****************' },

      // Basic SEO meta tags (will be overridden by i18n)
      { hid: 'author', name: 'author', content: 'yugiohcardmaker.org' },
      { hid: 'robots', name: 'robots', content: 'index, follow' },
      { hid: 'googlebot', name: 'googlebot', content: 'index, follow' },

      // Open Graph meta tags (static parts)
      { hid: 'og:type', property: 'og:type', content: 'website' },
      { hid: 'og:site_name', property: 'og:site_name', content: 'Yu-Gi-Oh! Card Maker' },
      { hid: 'og:url', property: 'og:url', content: `${baseUrl}/` },
      { hid: 'og:image', property: 'og:image', content: `${baseUrl}/images/og-image.jpg` },
      { hid: 'og:image:width', property: 'og:image:width', content: '1200' },
      { hid: 'og:image:height', property: 'og:image:height', content: '630' },

      // Twitter Card meta tags (static parts)
      { hid: 'twitter:card', name: 'twitter:card', content: 'summary_large_image' },
      { hid: 'twitter:site', name: 'twitter:site', content: '@yugiohcardmaker' },
      { hid: 'twitter:creator', name: 'twitter:creator', content: '@yugiohcardmaker' },
      { hid: 'twitter:image', name: 'twitter:image', content: `${baseUrl}/images/twitter-card.jpg` },

      // Additional SEO meta tags
      { hid: 'application-name', name: 'application-name', content: 'Yu-Gi-Oh! Card Maker' },
      { hid: 'apple-mobile-web-app-title', name: 'apple-mobile-web-app-title', content: 'Yu-Gi-Oh! Card Maker' },
      { hid: 'apple-mobile-web-app-capable', name: 'apple-mobile-web-app-capable', content: 'yes' },
      { hid: 'apple-mobile-web-app-status-bar-style', name: 'apple-mobile-web-app-status-bar-style', content: 'black-translucent' },
      { hid: 'msapplication-TileColor', name: 'msapplication-TileColor', content: '#2c3e50' },
      { hid: 'msapplication-config', name: 'msapplication-config', content: '/browserconfig.xml' }
    ],
    link: [
      // Favicon and app icons
      { rel: 'icon', type: 'image/x-icon', href: '/favicon.ico' },
      { rel: 'icon', type: 'image/png', sizes: '32x32', href: '/favicon-32x32.png' },
      { rel: 'icon', type: 'image/png', sizes: '16x16', href: '/favicon-16x16.png' },
      { rel: 'apple-touch-icon', sizes: '180x180', href: '/apple-touch-icon.png' },
      { rel: 'manifest', href: '/site.webmanifest' },

      // Preconnect to external domains for performance
      { rel: 'preconnect', href: 'https://fonts.googleapis.com' },
      { rel: 'preconnect', href: 'https://fonts.gstatic.com', crossorigin: true },

      // Canonical URL and hreflang links are now handled dynamically by each page
    ],
    script: [
      ...gaTags,
      // Google AdSense script
      {
        hid: 'google-adsense',
        src: 'https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-****************',
        async: true,
        crossorigin: 'anonymous'
      },
      // Structured data for SEO
      {
        hid: 'structured-data',
        type: 'application/ld+json',
        innerHTML: JSON.stringify({
          '@context': 'https://schema.org',
          '@type': 'WebApplication',
          name: 'Yu-Gi-Oh! Card Maker',
          alternateName: 'Yu-Gi-Oh! Card Maker',
          description: 'Free online Yu-Gi-Oh! card maker. Create custom monster, spell, and trap cards with professional quality.',
          url: `${baseUrl}/`,
          applicationCategory: 'DesignApplication',
          operatingSystem: 'Web Browser',
          offers: {
            '@type': 'Offer',
            price: '0',
            priceCurrency: 'USD'
          },
          author: {
            '@type': 'Organization',
            name: 'yugiohcardmaker.org',
            url: baseUrl
          },
          inLanguage: ['en', 'zh', 'ja'],
          isAccessibleForFree: true,
          screenshot: `${baseUrl}/images/screenshot.jpg`
        })
      }
    ],
    __dangerouslyDisableSanitizersByTagID: {
      'structured-data': ['innerHTML']
    }
  },

  // Global CSS: https://go.nuxtjs.dev/config-css
  css: [
    'bootstrap/dist/css/bootstrap.css',
    'bootstrap-vue/dist/bootstrap-vue.css'
  ],

  // Plugins to run before rendering page: https://go.nuxtjs.dev/config-plugins
  plugins: [
    {
      src: '~/plugins/font-awesome'
    },
    {
      src: '~/plugins/gtag',
      mode: 'client'
    }
  ],

  // Auto import components: https://go.nuxtjs.dev/config-components
  components: true,

  // Modules for dev and build (recommended): https://go.nuxtjs.dev/config-modules
  buildModules: [
    // https://go.nuxtjs.dev/eslint
    '@nuxtjs/eslint-module',

    'nuxt-font-loader',
  ],

  
  fontLoader : { 
    url: {
      local: 'fonts/font-face.css',
      google: 'https://fonts.googleapis.com/css2?family=Noto+Sans+JP:wght@100;300;400;500;700;900&family=Noto+Sans+SC:wght@100;300;400;500;700;900&family=Noto+Sans+TC:wght@100;300;400;500;700;900&display=swap',
    },
    prefetch : true,
    preconnect : true,
    preload: {
      hid: 'my-font-preload',
    },
  },

  // Router configuration
  router: {
    middleware: ['seo']
  },

  // Server Middleware
  serverMiddleware: [
    // Custom sitemap.xml handler
    '~/serverMiddleware/sitemap.js'
  ],

  // Modules: https://go.nuxtjs.dev/config-modules
  modules: [
    // https://go.nuxtjs.dev/bootstrap
    'bootstrap-vue/nuxt',
    // https://go.nuxtjs.dev/axios
    '@nuxtjs/axios',

    'nuxt-fontawesome',

    // Internationalization
    '@nuxtjs/i18n',

    // Sitemap generation - 禁用自动生成，使用自定义脚本
    // '@nuxtjs/sitemap',
  ],

  // Bootstrap Vue configuration
  bootstrapVue: {
    // Install the `IconsPlugin` plugin (in addition to `BootstrapVue` plugin)
    icons: false, // Disable icons to reduce bundle size
    // Tree-shake bootstrap-vue
    componentPlugins: [
      'LayoutPlugin',
      'FormPlugin',
      'FormCheckboxPlugin',
      'FormInputPlugin',
      'FormSelectPlugin',
      'FormTextareaPlugin',
      'FormFilePlugin',
      'ButtonPlugin',
      'CardPlugin',
      'ModalPlugin',
      'NavbarPlugin',
      'CollapsePlugin',
      'SpinnerPlugin'
    ],
    directivePlugins: [],
    // Disable CSS import to reduce bundle size
    bootstrapCSS: false,
    bootstrapVueCSS: false
  },

  fontawesome: {
    // icon 的標籤使用 <fa>，這邊不設定就會依照 plugin 裡的設定<font-awesome-icon>
    component: 'fa', 
    imports: [
      {
        set: '@fortawesome/free-solid-svg-icons',
        icons: ['fas']
      },
      {
        set: '@fortawesome/free-regular-svg-icons',
        icons: ['far']
      },
      {
        set: '@fortawesome/free-brands-svg-icons',
        icons: ['fab']
      },
    ]
  },

  // Axios module configuration: https://go.nuxtjs.dev/config-axios
  axios: {},

  // i18n configuration
  i18n: {
    locales: [
      {
        code: 'en',
        iso: 'en-US',
        name: 'English',
        file: 'en.js'
      },
      {
        code: 'zh',
        iso: 'zh-TW',
        name: '中文',
        file: 'zh.js'
      },
      {
        code: 'ja',
        iso: 'ja-JP',
        name: '日本語',
        file: 'ja.js'
      },
      {
        code: 'de',
        iso: 'de-DE',
        name: 'Deutsch',
        file: 'de.js'
      },
      {
        code: 'fr',
        iso: 'fr-FR',
        name: 'Français',
        file: 'fr.js'
      },
      {
        code: 'ko',
        iso: 'ko-KR',
        name: '한국어',
        file: 'ko.js'
      },
      {
        code: 'pt',
        iso: 'pt-PT',
        name: 'Português',
        file: 'pt.js'
      },
      {
        code: 'es',
        iso: 'es-ES',
        name: 'Español',
        file: 'es.js'
      },
      {
        code: 'el',
        iso: 'el-GR',
        name: 'Ελληνικά',
        file: 'el.js'
      },
      {
        code: 'th',
        iso: 'th-TH',
        name: 'ไทย',
        file: 'th.js'
      },
      {
        code: 'ru',
        iso: 'ru-RU',
        name: 'Русский',
        file: 'ru.js'
      },
      {
        code: 'vi',
        iso: 'vi-VN',
        name: 'Tiếng Việt',
        file: 'vi.js'
      }
    ],
    defaultLocale: 'en',
    strategy: 'prefix_except_default',
    langDir: 'locales/',
    lazy: true,
    detectBrowserLanguage: {
      useCookie: true,
      cookieKey: 'i18n_redirected',
      redirectOn: 'root',
      alwaysRedirect: false,
      fallbackLocale: 'en'
    },
    seo: true,
    baseUrl: baseUrl,
    // Enable head management for SEO
    vueI18n: {
      fallbackLocale: 'en'
    }
  },

  // Build Configuration: https://go.nuxtjs.dev/config-build
  build: {
    // PostCSS configuration - disabled to prevent warnings
    postcss: false,

    // Optimize for production builds
    extractCSS: true,

    // Enable CSS optimization
    optimizeCSS: true,

    // CDN-friendly asset configuration
    publicPath: process.env.DEPLOY_ENV === 'CLOUDFLARE_PAGES' ? '/_nuxt/' : '/_nuxt/',

    // Enable HTML minification
    html: {
      minify: {
        collapseBooleanAttributes: true,
        decodeEntities: true,
        minifyCSS: true,
        minifyJS: true,
        processConditionalComments: true,
        removeEmptyAttributes: true,
        removeRedundantAttributes: true,
        trimCustomFragments: true,
        useShortDoctype: true
      }
    },

    // Optimize bundle splitting
    splitChunks: {
      layouts: true,
      pages: true,
      commons: true
    },

    // Babel configuration to handle large files
    babel: {
      compact: false,
      presets({ isServer }) {
        return [
          [
            require.resolve('@nuxt/babel-preset-app'),
            {
              buildTarget: isServer ? 'server' : 'client',
              corejs: { version: 3 }
            }
          ]
        ]
      }
    },

    // Webpack optimization
    extend(config, { isDev, isClient }) {
      // Optimize for production
      if (!isDev && isClient) {
        config.optimization.splitChunks.maxSize = 200000

        // Enable tree shaking
        config.optimization.usedExports = true
        config.optimization.sideEffects = false

        // Optimize images with better caching
        config.module.rules.push({
          test: /\.(png|jpe?g|gif|svg|webp)$/i,
          use: [
            {
              loader: 'file-loader',
              options: {
                name: 'img/[name].[contenthash:8].[ext]',
                publicPath: '/_nuxt/'
              }
            }
          ]
        })

        // Optimize fonts for CDN
        config.module.rules.push({
          test: /\.(woff|woff2|eot|ttf|otf)$/i,
          use: [
            {
              loader: 'file-loader',
              options: {
                name: 'fonts/[name].[contenthash:8].[ext]',
                publicPath: '/_nuxt/'
              }
            }
          ]
        })

        // Enable long-term caching for Cloudflare Pages
        if (process.env.DEPLOY_ENV === 'CLOUDFLARE_PAGES') {
          config.output.filename = 'js/[name].[contenthash:8].js'
          config.output.chunkFilename = 'js/[name].[contenthash:8].js'
        }
      }

      // Handle large dependencies
      config.resolve.alias = {
        ...config.resolve.alias,
        'bootstrap-vue$': 'bootstrap-vue/dist/bootstrap-vue.esm.js'
      }
    },

    // Transpile specific packages
    transpile: [
      'bootstrap-vue'
    ]
  },

  // Generate configuration for static deployment
  generate: {
    dir: 'dist',
    fallback: true
  },

  // Render configuration for performance
  render: {
    // Enable compression
    compressor: { threshold: 0 },

    // Resource hints
    resourceHints: true,

    // Static file configuration
    static: {
      setHeaders(res, path) {
        if (path.endsWith('.xml')) {
          res.setHeader('Content-Type', 'text/xml; charset=utf-8')
        }
      }
    },

    // HTTP2 push
    http2: {
      push: true,
      pushAssets: (req, res, publicPath, preloadFiles) => {
        return preloadFiles
          .filter(f => f.asType === 'script' && f.file === 'runtime.js')
          .map(f => `<${publicPath}${f.file}>; rel=preload; as=${f.asType}`)
      }
    }
  },

  // Sitemap configuration - 已禁用，使用自定义脚本生成
  /*
  sitemap: {
    hostname: 'https://yugiohcardmaker.org',
    gzip: true,
    exclude: [
      '/analytics-test',
      '/privacy',
      '/terms'
    ],
    i18n: true,
    routes: [
      {
        url: '/',
        changefreq: 'weekly',
        priority: 1.0,
        lastmod: new Date().toISOString()
      }
    ]
  }
  */
}
