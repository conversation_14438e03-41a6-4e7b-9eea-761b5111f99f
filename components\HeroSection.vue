<template>
  <section class="hero-section" role="banner">
    <div class="hero-background">
      <div class="hero-overlay"></div>
    </div>
    
    <div class="container">
      <div class="row align-items-center min-vh-50">
        <div class="col-lg-6 col-md-8 mx-auto text-center">
          <header class="hero-content">
            <h1 class="hero-title display-4 font-weight-bold text-white mb-4">
              {{ heroData.title }}
            </h1>
            
            <p class="hero-subtitle h4 text-light mb-4">
              {{ heroData.subtitle }}
            </p>
            
            <p class="hero-description lead text-light mb-5">
              {{ heroData.description }}
            </p>
            
            <div class="hero-actions">
              <button 
                @click="scrollToEditor"
                class="btn btn-primary btn-lg me-3 mb-3"
                :aria-label="heroData.cta_button"
              >
                <fa :icon="['fas', 'play']" class="me-2" />
                {{ heroData.cta_button }}
              </button>
              
              <button 
                @click="scrollToFeatures"
                class="btn btn-outline-light btn-lg mb-3"
                :aria-label="heroData.demo_button"
              >
                <fa :icon="['fas', 'info-circle']" class="me-2" />
                {{ heroData.demo_button }}
              </button>
            </div>
          </header>
        </div>
      </div>
      
      <!-- Scroll indicator -->
      <div class="scroll-indicator">
        <button 
          @click="scrollToFeatures"
          class="scroll-btn"
          aria-label="向下滾動查看更多內容"
        >
          <fa :icon="['fas', 'chevron-down']" class="scroll-icon" />
        </button>
      </div>
    </div>
  </section>
</template>

<script>
import { mapGetters } from 'vuex'

export default {
  name: 'HeroSection',
  
  computed: {
    ...mapGetters(['currentUIData']),
    
    heroData() {
      return this.currentUIData.sections?.hero || {
        title: '遊戲王卡片製造機',
        subtitle: '創造屬於你的專屬卡片',
        description: '專業的在線卡片設計工具，支持所有遊戲王卡片類型，免費使用，高質量輸出。',
        cta_button: '開始製作',
        demo_button: '查看演示'
      }
    }
  },
  
  methods: {
    /**
     * 滾動到編輯器區域
     */
    scrollToEditor() {
      const editorElement = document.getElementById('card-editor')
      if (editorElement) {
        editorElement.scrollIntoView({ 
          behavior: 'smooth',
          block: 'start'
        })
      }
    },
    
    /**
     * 滾動到功能介紹區域
     */
    scrollToFeatures() {
      const featuresElement = document.getElementById('features-section')
      if (featuresElement) {
        featuresElement.scrollIntoView({ 
          behavior: 'smooth',
          block: 'start'
        })
      }
    }
  }
}
</script>

<style scoped>
.hero-section {
  position: relative;
  min-height: 60vh;
  display: flex;
  align-items: center;
  overflow: hidden;
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  background-size: 400% 400%;
  animation: gradientShift 15s ease infinite;
}

.hero-background::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: 
    radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(120, 119, 198, 0.2) 0%, transparent 50%);
}

.hero-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
}

.hero-content {
  position: relative;
  z-index: 2;
}

.hero-title {
  font-size: 3.5rem;
  font-weight: 700;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
  margin-bottom: 1.5rem;
  animation: fadeInUp 1s ease-out;
}

.hero-subtitle {
  font-size: 1.5rem;
  font-weight: 300;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
  margin-bottom: 1.5rem;
  animation: fadeInUp 1s ease-out 0.2s both;
}

.hero-description {
  font-size: 1.1rem;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
  margin-bottom: 2.5rem;
  animation: fadeInUp 1s ease-out 0.4s both;
}

.hero-actions {
  animation: fadeInUp 1s ease-out 0.6s both;
}

.btn-primary {
  background: linear-gradient(45deg, #007bff, #0056b3);
  border: none;
  box-shadow: 0 4px 15px rgba(0, 123, 255, 0.4);
  transition: all 0.3s ease;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 123, 255, 0.6);
}

.btn-outline-light {
  border: 2px solid rgba(255, 255, 255, 0.8);
  transition: all 0.3s ease;
}

.btn-outline-light:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: white;
  transform: translateY(-2px);
}

.scroll-indicator {
  position: absolute;
  bottom: 2rem;
  left: 50%;
  transform: translateX(-50%);
  z-index: 2;
}

.scroll-btn {
  background: none;
  border: none;
  color: white;
  font-size: 1.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  animation: bounce 2s infinite;
}

.scroll-btn:hover {
  color: #007bff;
  transform: scale(1.1);
}

.scroll-icon {
  display: block;
}

/* Animations */
@keyframes gradientShift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

/* Responsive design */
@media (max-width: 768px) {
  .hero-title {
    font-size: 2.5rem;
  }
  
  .hero-subtitle {
    font-size: 1.25rem;
  }
  
  .hero-description {
    font-size: 1rem;
  }
  
  .btn-lg {
    padding: 0.75rem 1.5rem;
    font-size: 1rem;
  }
  
  .hero-actions .btn {
    display: block;
    width: 100%;
    margin-bottom: 1rem;
  }
  
  .hero-actions .btn:last-child {
    margin-bottom: 0;
  }
}

@media (max-width: 576px) {
  .hero-title {
    font-size: 2rem;
  }
  
  .hero-subtitle {
    font-size: 1.1rem;
  }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  .hero-background,
  .hero-title,
  .hero-subtitle,
  .hero-description,
  .hero-actions,
  .scroll-btn {
    animation: none;
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .hero-background {
    background: #000;
  }
  
  .hero-overlay {
    background: rgba(0, 0, 0, 0.8);
  }
  
  .btn-primary {
    background: #0066cc;
    border: 2px solid #ffffff;
  }
  
  .btn-outline-light {
    border-color: #ffffff;
    color: #ffffff;
  }
}
</style>
