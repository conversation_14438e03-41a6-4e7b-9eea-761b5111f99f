# Cloudflare Pages Headers Configuration

# Cache static assets for 1 year
/fonts/*
  Cache-Control: public, max-age=31536000, immutable

/images/*
  Cache-Control: public, max-age=31536000, immutable

/_nuxt/*
  Cache-Control: public, max-age=31536000, immutable

/css/*
  Cache-Control: public, max-age=31536000, immutable

# Cache data files for 1 day
/ygo/*
  Cache-Control: public, max-age=86400

# Security headers for all pages
/*
  X-Frame-Options: DENY
  X-Content-Type-Options: nosniff
  Referrer-Policy: strict-origin-when-cross-origin
  Permissions-Policy: camera=(), microphone=(), geolocation=()
  X-Robots-Tag: index, follow

# Sitemap headers
/*.xml
  Content-Type: text/xml; charset=utf-8
  Cache-Control: public, max-age=3600
  X-Robots-Tag: noindex

# Main pages - allow indexing
/
  X-Robots-Tag: index, follow, max-snippet:-1, max-image-preview:large, max-video-preview:-1

/*/
  X-Robots-Tag: index, follow, max-snippet:-1, max-image-preview:large, max-video-preview:-1

# Privacy and Terms pages - allow indexing but lower priority
/privacy
  X-Robots-Tag: index, follow, max-snippet:160

/*/privacy
  X-Robots-Tag: index, follow, max-snippet:160

/terms
  X-Robots-Tag: index, follow, max-snippet:160

/*/terms
  X-Robots-Tag: index, follow, max-snippet:160

# Analytics test page - no indexing
/analytics-test
  X-Robots-Tag: noindex, nofollow

/*/analytics-test
  X-Robots-Tag: noindex, nofollow

# Robots.txt
/robots.txt
  Content-Type: text/plain; charset=utf-8
  Cache-Control: public, max-age=86400
  X-Robots-Tag: noindex
