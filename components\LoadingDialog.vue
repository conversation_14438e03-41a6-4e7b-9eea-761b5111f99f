<template>
  <div>
    <!-- Modal主題 -->
    <b-modal
      v-model="show"
      hide-header
      hide-footer
      centered
      :no-close-on-backdrop="true"
    >
      <b-row>
        <b-col class="text-center py-3">
          <!-- 进度指示器 -->
          <div class="progress-container mb-3">
            <div class="progress-bar" :style="{ width: progressPercentage + '%' }"></div>
          </div>

          <!-- 加载动画 -->
          <div class="text-center mt-3 mb-1">
            <b-spinner small type="grow" label="Loading"></b-spinner>&nbsp;
            <b-spinner small variant="primary" type="grow" label="Loading"></b-spinner>&nbsp;
            <b-spinner small variant="success" type="grow" label="Loading"></b-spinner>&nbsp;
            <b-spinner small variant="danger" type="grow" label="Loading"></b-spinner>&nbsp;
            <b-spinner small variant="warning" type="grow" label="Loading"></b-spinner>
          </div>

          <!-- 加载状态文本 -->
          <div class="dialog-content mt-3 mb-1">
            {{ currentLoadingMessage }}<br>
            <small class="loading-detail">{{ currentLoadingDetail }}</small>
          </div>

          <!-- 进度百分比 -->
          <div class="progress-text mt-2">
            <small>{{ Math.round(progressPercentage) }}%</small>
          </div>
        </b-col>
      </b-row>
    </b-modal>
  </div>
</template>

<script>
import { mapGetters, mapMutations } from 'vuex'

export default {
  data() {
    return {
      progress: 0,
      loadingStage: 'initializing',
      startTime: null,
      progressTimer: null
    }
  },

  computed: {
    ...mapGetters(['loadingDialogShow', 'currentUIData']),

    show: {
      get() {
        return this.loadingDialogShow
      },
      set(_) {
        this.closeLoadingDialog()
      },
    },

    progressPercentage() {
      return Math.min(this.progress, 100)
    },

    currentLoadingMessage() {
      const messages = {
        initializing: this.currentUIData.loading_initializing || 'Initializing...',
        fonts: this.currentUIData.loading_fonts || 'Loading fonts...',
        images: this.currentUIData.loading_images || 'Loading images...',
        rendering: this.currentUIData.loading_rendering || 'Rendering card...',
        complete: this.currentUIData.loading_complete || 'Almost ready!'
      }
      return messages[this.loadingStage] || this.currentUIData.loading_message || 'Please wait'
    },

    currentLoadingDetail() {
      const details = {
        initializing: this.currentUIData.loading_detail_init || 'Setting up the card maker...',
        fonts: this.currentUIData.loading_detail_fonts || 'Loading custom fonts for card text...',
        images: this.currentUIData.loading_detail_images || 'Preparing card templates and graphics...',
        rendering: this.currentUIData.loading_detail_render || 'Drawing your card...',
        complete: this.currentUIData.loading_detail_complete || 'Finalizing...'
      }
      return details[this.loadingStage] || this.currentUIData.loading_note || 'This may take a moment on first visit'
    }
  },

  watch: {
    loadingDialogShow(newVal) {
      if (newVal) {
        this.startLoading()
      } else {
        this.stopLoading()
      }
    }
  },

  methods: {
    ...mapMutations(['closeLoadingDialog']),

    startLoading() {
      this.progress = 0
      this.loadingStage = 'initializing'
      this.startTime = Date.now()

      // 模拟加载进度
      this.simulateProgress()
    },

    stopLoading() {
      if (this.progressTimer) {
        clearInterval(this.progressTimer)
        this.progressTimer = null
      }
    },

    simulateProgress() {
      this.progressTimer = setInterval(() => {
        const elapsed = Date.now() - this.startTime

        // 根据时间和阶段更新进度
        if (elapsed < 500) {
          this.progress = Math.min(20, elapsed / 25)
          this.loadingStage = 'initializing'
        } else if (elapsed < 1500) {
          this.progress = Math.min(50, 20 + (elapsed - 500) / 20)
          this.loadingStage = 'fonts'
        } else if (elapsed < 2500) {
          this.progress = Math.min(80, 50 + (elapsed - 1500) / 33)
          this.loadingStage = 'images'
        } else if (elapsed < 3000) {
          this.progress = Math.min(95, 80 + (elapsed - 2500) / 33)
          this.loadingStage = 'rendering'
        } else {
          this.progress = 100
          this.loadingStage = 'complete'

          // 自动关闭
          setTimeout(() => {
            if (this.loadingDialogShow) {
              this.closeLoadingDialog()
            }
          }, 500)
        }
      }, 50)
    }
  },

  beforeDestroy() {
    this.stopLoading()
  }
}
</script>

<style scoped>
>>> .modal-content, >>> .modal-body {
  background: #333333 !important;
  border-radius: 1rem;
  border: none;
  box-shadow: 1px 1px 20px #222222A6;
  -webkit-box-shadow: 1px 1px 20px #222222A6;
	-moz-box-shadow: 1px 1px 20px #222222A6;
}

.dialog-content {
  color: #CCC;
  font-size: 18px;
  font-weight: 500;
}

.loading-detail {
  color: #999;
  font-size: 14px;
  opacity: 0.8;
}

.progress-container {
  width: 100%;
  height: 6px;
  background-color: #555;
  border-radius: 3px;
  overflow: hidden;
  position: relative;
}

.progress-bar {
  height: 100%;
  background: linear-gradient(90deg, #007bff, #28a745, #ffc107);
  border-radius: 3px;
  transition: width 0.3s ease;
  position: relative;
}

.progress-bar::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
  animation: shimmer 2s infinite;
}

.progress-text {
  color: #999;
  font-size: 12px;
  font-weight: 600;
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

/* 优化加载动画 */
>>> .spinner-grow {
  animation-duration: 1.5s;
}
</style>