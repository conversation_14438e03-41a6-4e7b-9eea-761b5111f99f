/**
 * 性能监控工具
 * 用于监控和分析网站首次加载性能
 */

export class PerformanceMonitor {
  constructor() {
    this.metrics = {}
    this.startTime = performance.now()
    this.observers = []
    this.isMonitoring = false
  }

  /**
   * 开始性能监控
   */
  startMonitoring() {
    if (this.isMonitoring) return
    
    this.isMonitoring = true
    this.startTime = performance.now()
    
    // 监控关键性能指标
    this.monitorWebVitals()
    this.monitorResourceLoading()
    this.monitorFontLoading()
    this.monitorFirstPaint()
    
    console.log('🚀 Performance monitoring started')
  }

  /**
   * 停止性能监控
   */
  stopMonitoring() {
    this.isMonitoring = false
    this.observers.forEach(observer => observer.disconnect())
    this.observers = []
    
    console.log('⏹️ Performance monitoring stopped')
  }

  /**
   * 记录性能指标
   */
  recordMetric(name, value, unit = 'ms') {
    const timestamp = performance.now()
    this.metrics[name] = {
      value,
      unit,
      timestamp,
      relativeTime: timestamp - this.startTime
    }
    
    console.log(`📊 ${name}: ${value}${unit}`)
  }

  /**
   * 监控Web Vitals指标
   */
  monitorWebVitals() {
    // First Contentful Paint (FCP)
    if ('PerformanceObserver' in window) {
      const fcpObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries()
        entries.forEach(entry => {
          if (entry.name === 'first-contentful-paint') {
            this.recordMetric('FCP', Math.round(entry.startTime))
          }
        })
      })
      
      try {
        fcpObserver.observe({ entryTypes: ['paint'] })
        this.observers.push(fcpObserver)
      } catch (e) {
        console.warn('FCP monitoring not supported')
      }
    }

    // Largest Contentful Paint (LCP)
    if ('PerformanceObserver' in window) {
      const lcpObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries()
        const lastEntry = entries[entries.length - 1]
        if (lastEntry) {
          this.recordMetric('LCP', Math.round(lastEntry.startTime))
        }
      })
      
      try {
        lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] })
        this.observers.push(lcpObserver)
      } catch (e) {
        console.warn('LCP monitoring not supported')
      }
    }
  }

  /**
   * 监控资源加载
   */
  monitorResourceLoading() {
    if ('PerformanceObserver' in window) {
      const resourceObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries()
        
        entries.forEach(entry => {
          const resourceType = this.getResourceType(entry.name)
          const loadTime = entry.responseEnd - entry.startTime
          
          if (resourceType) {
            this.recordMetric(`${resourceType}_load_time`, Math.round(loadTime))
          }
        })
      })
      
      try {
        resourceObserver.observe({ entryTypes: ['resource'] })
        this.observers.push(resourceObserver)
      } catch (e) {
        console.warn('Resource monitoring not supported')
      }
    }
  }

  /**
   * 监控字体加载
   */
  monitorFontLoading() {
    if (document.fonts && document.fonts.ready) {
      const fontLoadStart = performance.now()
      
      document.fonts.ready.then(() => {
        const fontLoadTime = performance.now() - fontLoadStart
        this.recordMetric('font_load_time', Math.round(fontLoadTime))
      })
    }
  }

  /**
   * 监控首次绘制
   */
  monitorFirstPaint() {
    if ('PerformanceObserver' in window) {
      const paintObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries()
        entries.forEach(entry => {
          if (entry.name === 'first-paint') {
            this.recordMetric('FP', Math.round(entry.startTime))
          }
        })
      })
      
      try {
        paintObserver.observe({ entryTypes: ['paint'] })
        this.observers.push(paintObserver)
      } catch (e) {
        console.warn('Paint monitoring not supported')
      }
    }
  }

  /**
   * 获取资源类型
   */
  getResourceType(url) {
    if (url.includes('/fonts/')) return 'font'
    if (url.includes('/images/')) return 'image'
    if (url.includes('.css')) return 'css'
    if (url.includes('.js')) return 'js'
    return null
  }

  /**
   * 记录加载阶段
   */
  recordLoadingStage(stage) {
    const timestamp = performance.now()
    this.recordMetric(`stage_${stage}`, Math.round(timestamp - this.startTime))
  }

  /**
   * 记录Canvas渲染时间
   */
  recordCanvasRenderTime(startTime) {
    const renderTime = performance.now() - startTime
    this.recordMetric('canvas_render_time', Math.round(renderTime))
  }

  /**
   * 获取性能报告
   */
  getPerformanceReport() {
    const report = {
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      metrics: { ...this.metrics },
      summary: this.generateSummary()
    }
    
    return report
  }

  /**
   * 生成性能摘要
   */
  generateSummary() {
    const summary = {
      totalLoadTime: this.metrics.stage_complete?.value || 0,
      fontLoadTime: this.metrics.font_load_time?.value || 0,
      canvasRenderTime: this.metrics.canvas_render_time?.value || 0,
      fcp: this.metrics.FCP?.value || 0,
      lcp: this.metrics.LCP?.value || 0
    }

    // 性能评级
    summary.grade = this.calculateGrade(summary)
    
    return summary
  }

  /**
   * 计算性能评级
   */
  calculateGrade(summary) {
    let score = 100
    
    // FCP评分 (目标: < 1.8s)
    if (summary.fcp > 3000) score -= 30
    else if (summary.fcp > 1800) score -= 15
    
    // LCP评分 (目标: < 2.5s)
    if (summary.lcp > 4000) score -= 30
    else if (summary.lcp > 2500) score -= 15
    
    // 总加载时间评分 (目标: < 3s)
    if (summary.totalLoadTime > 5000) score -= 25
    else if (summary.totalLoadTime > 3000) score -= 10
    
    // 字体加载时间评分 (目标: < 1s)
    if (summary.fontLoadTime > 2000) score -= 15
    else if (summary.fontLoadTime > 1000) score -= 5
    
    if (score >= 90) return 'A'
    if (score >= 80) return 'B'
    if (score >= 70) return 'C'
    if (score >= 60) return 'D'
    return 'F'
  }

  /**
   * 输出性能报告到控制台
   */
  logPerformanceReport() {
    const report = this.getPerformanceReport()
    
    console.group('🎯 Performance Report')
    console.log('📊 Summary:', report.summary)
    console.log('📈 Detailed Metrics:', report.metrics)
    console.log(`🏆 Performance Grade: ${report.summary.grade}`)
    console.groupEnd()
    
    return report
  }

  /**
   * 检查是否为首次访问
   */
  isFirstVisit() {
    return !localStorage.getItem('visited_before')
  }

  /**
   * 标记已访问
   */
  markAsVisited() {
    localStorage.setItem('visited_before', 'true')
  }
}

// 创建全局实例
export const performanceMonitor = new PerformanceMonitor()

// 默认导出
export default performanceMonitor
