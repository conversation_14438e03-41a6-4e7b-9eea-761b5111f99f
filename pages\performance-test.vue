<template>
  <div class="performance-test-page">
    <div class="container mt-5">
      <div class="row">
        <div class="col-12">
          <h1 class="text-center mb-4">性能测试页面</h1>
          <p class="text-center text-muted mb-5">
            此页面用于测试和验证网站首次加载性能优化效果
          </p>
        </div>
      </div>

      <div class="row">
        <div class="col-md-6">
          <div class="card">
            <div class="card-header">
              <h3>性能指标</h3>
            </div>
            <div class="card-body">
              <div v-if="performanceData">
                <div class="metric-item" v-for="(metric, key) in performanceData.metrics" :key="key">
                  <strong>{{ formatMetricName(key) }}:</strong>
                  <span class="metric-value">{{ metric.value }}{{ metric.unit }}</span>
                </div>
              </div>
              <div v-else class="text-muted">
                正在收集性能数据...
              </div>
            </div>
          </div>
        </div>

        <div class="col-md-6">
          <div class="card">
            <div class="card-header">
              <h3>性能摘要</h3>
            </div>
            <div class="card-body">
              <div v-if="performanceSummary">
                <div class="summary-item">
                  <strong>性能评级:</strong>
                  <span class="grade" :class="'grade-' + performanceSummary.grade">
                    {{ performanceSummary.grade }}
                  </span>
                </div>
                <div class="summary-item">
                  <strong>总加载时间:</strong>
                  <span>{{ performanceSummary.totalLoadTime }}ms</span>
                </div>
                <div class="summary-item">
                  <strong>字体加载时间:</strong>
                  <span>{{ performanceSummary.fontLoadTime }}ms</span>
                </div>
                <div class="summary-item">
                  <strong>Canvas渲染时间:</strong>
                  <span>{{ performanceSummary.canvasRenderTime }}ms</span>
                </div>
                <div class="summary-item">
                  <strong>首次内容绘制 (FCP):</strong>
                  <span>{{ performanceSummary.fcp }}ms</span>
                </div>
                <div class="summary-item">
                  <strong>最大内容绘制 (LCP):</strong>
                  <span>{{ performanceSummary.lcp }}ms</span>
                </div>
              </div>
              <div v-else class="text-muted">
                等待性能数据...
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="row mt-4">
        <div class="col-12">
          <div class="card">
            <div class="card-header">
              <h3>测试操作</h3>
            </div>
            <div class="card-body">
              <div class="btn-group" role="group">
                <button 
                  class="btn btn-primary" 
                  @click="runPerformanceTest"
                  :disabled="isTestRunning"
                >
                  {{ isTestRunning ? '测试中...' : '运行性能测试' }}
                </button>
                <button 
                  class="btn btn-secondary" 
                  @click="clearCache"
                >
                  清除缓存
                </button>
                <button 
                  class="btn btn-info" 
                  @click="exportReport"
                >
                  导出报告
                </button>
                <button 
                  class="btn btn-success" 
                  @click="goToMainPage"
                >
                  返回主页
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="row mt-4" v-if="testResults.length > 0">
        <div class="col-12">
          <div class="card">
            <div class="card-header">
              <h3>测试历史</h3>
            </div>
            <div class="card-body">
              <div class="table-responsive">
                <table class="table table-striped">
                  <thead>
                    <tr>
                      <th>时间</th>
                      <th>评级</th>
                      <th>总加载时间</th>
                      <th>FCP</th>
                      <th>LCP</th>
                      <th>字体加载</th>
                      <th>Canvas渲染</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr v-for="(result, index) in testResults" :key="index">
                      <td>{{ formatTime(result.timestamp) }}</td>
                      <td>
                        <span class="grade" :class="'grade-' + result.summary.grade">
                          {{ result.summary.grade }}
                        </span>
                      </td>
                      <td>{{ result.summary.totalLoadTime }}ms</td>
                      <td>{{ result.summary.fcp }}ms</td>
                      <td>{{ result.summary.lcp }}ms</td>
                      <td>{{ result.summary.fontLoadTime }}ms</td>
                      <td>{{ result.summary.canvasRenderTime }}ms</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import performanceMonitor from '~/utils/performanceMonitor.js'

export default {
  name: 'PerformanceTestPage',
  
  head() {
    return {
      title: '性能测试 - Yu-Gi-Oh! Card Maker',
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: '性能测试页面，用于验证网站加载性能优化效果'
        },
        {
          name: 'robots',
          content: 'noindex, nofollow'
        }
      ]
    }
  },

  data() {
    return {
      performanceData: null,
      performanceSummary: null,
      isTestRunning: false,
      testResults: []
    }
  },

  mounted() {
    this.loadPerformanceData()
    this.loadTestHistory()
  },

  methods: {
    loadPerformanceData() {
      // 获取当前性能数据
      setTimeout(() => {
        const report = performanceMonitor.getPerformanceReport()
        this.performanceData = report
        this.performanceSummary = report.summary
      }, 1000)
    },

    async runPerformanceTest() {
      this.isTestRunning = true
      
      try {
        // 清除当前性能数据
        performanceMonitor.stopMonitoring()
        
        // 重新开始监控
        performanceMonitor.startMonitoring()
        
        // 模拟页面重新加载的性能测试
        await this.simulatePageLoad()
        
        // 获取测试结果
        const report = performanceMonitor.getPerformanceReport()
        this.testResults.unshift(report)
        
        // 保存测试历史
        this.saveTestHistory()
        
        // 更新显示数据
        this.performanceData = report
        this.performanceSummary = report.summary
        
      } catch (error) {
        console.error('Performance test failed:', error)
      } finally {
        this.isTestRunning = false
      }
    },

    async simulatePageLoad() {
      // 模拟各种加载阶段
      const stages = ['start', 'fonts', 'images', 'rendering', 'complete']
      
      for (let i = 0; i < stages.length; i++) {
        await new Promise(resolve => setTimeout(resolve, 200 + Math.random() * 300))
        performanceMonitor.recordLoadingStage(stages[i])
      }
    },

    clearCache() {
      // 清除各种缓存
      if ('caches' in window) {
        caches.keys().then(names => {
          names.forEach(name => caches.delete(name))
        })
      }
      
      localStorage.removeItem('visited_before')
      sessionStorage.clear()
      
      alert('缓存已清除，建议刷新页面进行测试')
    },

    exportReport() {
      const report = this.performanceData
      if (!report) return
      
      const dataStr = JSON.stringify(report, null, 2)
      const dataBlob = new Blob([dataStr], { type: 'application/json' })
      
      const link = document.createElement('a')
      link.href = URL.createObjectURL(dataBlob)
      link.download = `performance-report-${new Date().toISOString()}.json`
      link.click()
    },

    goToMainPage() {
      this.$router.push('/')
    },

    formatMetricName(key) {
      const nameMap = {
        'FCP': '首次内容绘制',
        'LCP': '最大内容绘制',
        'FP': '首次绘制',
        'font_load_time': '字体加载时间',
        'canvas_render_time': 'Canvas渲染时间',
        'stage_start': '开始阶段',
        'stage_complete': '完成阶段'
      }
      return nameMap[key] || key
    },

    formatTime(timestamp) {
      return new Date(timestamp).toLocaleTimeString()
    },

    saveTestHistory() {
      localStorage.setItem('performance_test_history', JSON.stringify(this.testResults.slice(0, 10)))
    },

    loadTestHistory() {
      const history = localStorage.getItem('performance_test_history')
      if (history) {
        this.testResults = JSON.parse(history)
      }
    }
  }
}
</script>

<style scoped>
.performance-test-page {
  min-height: 100vh;
  background-color: #f8f9fa;
}

.card {
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  border: none;
  margin-bottom: 1rem;
}

.card-header {
  background-color: #007bff;
  color: white;
  font-weight: bold;
}

.metric-item, .summary-item {
  display: flex;
  justify-content: space-between;
  padding: 0.5rem 0;
  border-bottom: 1px solid #eee;
}

.metric-value {
  font-family: monospace;
  font-weight: bold;
}

.grade {
  font-weight: bold;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
}

.grade-A { background-color: #28a745; color: white; }
.grade-B { background-color: #17a2b8; color: white; }
.grade-C { background-color: #ffc107; color: black; }
.grade-D { background-color: #fd7e14; color: white; }
.grade-F { background-color: #dc3545; color: white; }

.table th {
  background-color: #f8f9fa;
  font-weight: 600;
}
</style>
